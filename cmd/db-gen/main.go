package main

import (
	"context"
	"flag"

	"gitlab.futunn.com/infra/frpc/pkg/application"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/repo/qtcard"
	"gorm.io/gen"
	"gorm.io/gorm"
)

var configPath string

var dataMap = map[string]func(gorm.ColumnType) (dataType string){
	"decimal": func(columnType gorm.ColumnType) (dataType string) {
		return "decimal.Decimal"
	},
	"tinyint": func(columnType gorm.ColumnType) (dataType string) {
		return "int32"
	},
}

func init() {
	flag.StringVar(&configPath, "config", "conf/conf.toml", "config file path")
	flag.Parse()
}

func start() int {
	ctx := context.Background()

	// see: https://gorm.io/zh_CN/gen/database_to_structs.html
	g := gen.NewGenerator(gen.Config{
		OutPath:       "./internal/app/repo/qtcard/gen",
		ModelPkgPath:  "./internal/app/model/db/qtcard",
		FieldNullable: true,
		FieldSignable: true,
		// Mode:          gen.WithDefaultQuery, // generate mode
	})

	g.WithDataTypeMap(dataMap)

	db := qtcard.NewQtCardDBRead(ctx)

	g.UseDB(db)

	g.ApplyBasic(
		g.GenerateModel("user_pri_end_time"),
		g.GenerateModel("quote_type"),
	)

	// Generate the code
	g.Execute()

	return 0
}

func main() {
	application.Run(
		start,
		application.WithConfigFile(configPath), // 指定配置文件的路径
		application.OnLoadConfig(
			application.DeleteServerConfig(),
		),
	)
}
