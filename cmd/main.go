package main

import (
	"flag"

	"gitlab.futunn.com/infra/frpc/pkg/application"
	_ "gitlab.futunn.com/infra/frpc/pkg/server/xgin"
	_ "gitlab.futunn.com/infra/frpc/pkg/server/xrpc"
	_ "gitlab.futunn.com/infra/frpc/pkg/util/banner"
	_ "gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/middleware"
	_ "gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/service"
	_ "gitlab.futunn.com/web_data_application/golib/gormcallback/sqlmonitor"
)

var configPath string

func init() {
	flag.StringVar(&configPath, "config", "conf/conf.toml", "config file path")
	flag.Parse()
}

func main() {
	application.Run(
		application.WaitShutdownSignals,        // 接收到退出信号后执行退出流程
		application.WithConfigFile(configPath), // 指定配置文件的路径
	)
}
