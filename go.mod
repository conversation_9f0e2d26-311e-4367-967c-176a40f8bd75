module gitlab.futunn.com/web_data_application/card_privilege_sync_service

go 1.22

require (
	github.com/coocood/freecache v1.1.1
	github.com/go-kit/kit v0.13.0
	github.com/samber/lo v1.47.0
	github.com/stretchr/testify v1.10.0
	gitlab.futunn.com/artifact-go/********************service/api v0.0.0-20240914095709-9e412832afab
	gitlab.futunn.com/artifact-go/********************service/pb v0.0.0-20240914095709-9e412832afab
	gitlab.futunn.com/artifact-go/common/srpc v1.0.85
	gitlab.futunn.com/artifact-go/user_attribution/api v1.1.0
	gitlab.futunn.com/artifact-go/user_attribution/pb v1.1.0
	gitlab.futunn.com/go-libs/infra/appinfo v0.1.2
	gitlab.futunn.com/go-libs/infra/errors v0.0.10
	gitlab.futunn.com/go-libs/infra/metadata/v2 v2.1.4
	gitlab.futunn.com/golang/fls v0.0.31
	gitlab.futunn.com/golang/ftrace v0.24.1
	gitlab.futunn.com/infra/frpc v1.29.0
	gitlab.futunn.com/web_data_application/golib v1.0.0
	google.golang.org/protobuf v1.33.0
	gorm.io/gen v0.3.26
	gorm.io/gorm v1.25.11
	gorm.io/plugin/dbresolver v1.5.0
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/ClickHouse/ch-go v0.61.5 // indirect
	github.com/ClickHouse/clickhouse-go/v2 v2.25.0 // indirect
	github.com/PuerkitoBio/rehttp v1.1.0 // indirect
	github.com/alibaba/sentinel-golang v1.0.2 // indirect
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/armon/go-metrics v0.4.0 // indirect
	github.com/aws/aws-sdk-go-v2 v1.30.5 // indirect
	github.com/aws/aws-sdk-go-v2/config v1.27.33 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.17.32 // indirect
	github.com/aws/aws-sdk-go-v2/feature/ec2/imds v1.16.13 // indirect
	github.com/aws/aws-sdk-go-v2/feature/rds/auth v1.4.17 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.17 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.17 // indirect
	github.com/aws/aws-sdk-go-v2/internal/ini v1.8.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.11.4 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.11.19 // indirect
	github.com/aws/aws-sdk-go-v2/service/sso v1.22.7 // indirect
	github.com/aws/aws-sdk-go-v2/service/ssooidc v1.26.7 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.30.7 // indirect
	github.com/aws/smithy-go v1.20.4 // indirect
	github.com/bytedance/gopkg v0.1.1 // indirect
	github.com/bytedance/sonic v1.13.1 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cenkalti/backoff v2.2.1+incompatible // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/fatih/color v1.13.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/gin-contrib/pprof v1.3.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.7.7 // indirect
	github.com/go-faster/city v1.0.1 // indirect
	github.com/go-faster/errors v0.7.1 // indirect
	github.com/go-kit/log v0.2.0 // indirect
	github.com/go-logfmt/logfmt v0.5.1 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.13.0 // indirect
	github.com/go-playground/universal-translator v0.17.0 // indirect
	github.com/go-playground/validator/v10 v10.4.1 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gookit/color v1.5.2 // indirect
	github.com/grafana/pyroscope-go v1.2.0 // indirect
	github.com/grafana/pyroscope-go/godeltaprof v0.1.8 // indirect
	github.com/hashicorp/consul/api v1.14.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-hclog v1.2.2 // indirect
	github.com/hashicorp/go-immutable-radix v1.3.1 // indirect
	github.com/hashicorp/go-rootcerts v1.0.2 // indirect
	github.com/hashicorp/go-version v1.7.0 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/hashicorp/serf v0.10.0 // indirect
	github.com/inhies/go-bytesize v0.0.0-20201103132853-d0aed0d254f8 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/klauspost/cpuid/v2 v2.0.9 // indirect
	github.com/knadh/koanf v1.4.2 // indirect
	github.com/leodido/go-urn v1.2.0 // indirect
	github.com/leonelquinteros/gotext v1.5.2 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.17 // indirect
	github.com/mitchellh/copystructure v1.2.0 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/mitchellh/reflectwalk v1.0.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/panjf2000/ants/v2 v2.4.3 // indirect
	github.com/paulmach/orb v0.11.1 // indirect
	github.com/pelletier/go-toml v1.7.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.22 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/segmentio/asm v1.2.0 // indirect
	github.com/shirou/gopsutil v3.21.11+incompatible // indirect
	github.com/shopspring/decimal v1.4.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.1.7 // indirect
	github.com/valyala/fastrand v1.1.0 // indirect
	github.com/valyala/histogram v1.2.0 // indirect
	github.com/xo/terminfo v0.0.0-20210125001918-ca9a967f8778 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	gitlab.futunn.com/artifact-go/common/web v1.0.15 // indirect
	gitlab.futunn.com/artifact-go/fns-agent/api v1.0.3 // indirect
	gitlab.futunn.com/artifact-go/fns-agent/pb v1.0.3 // indirect
	gitlab.futunn.com/artifact-go/rate-limit-token-server/api v1.0.5 // indirect
	gitlab.futunn.com/artifact-go/rate-limit-token-server/pb v1.0.5 // indirect
	gitlab.futunn.com/artifact-go/srpc_health/pb v1.0.4 // indirect
	gitlab.futunn.com/go-libs/infra/backward v0.0.2 // indirect
	gitlab.futunn.com/go-libs/infra/http v0.0.2 // indirect
	gitlab.futunn.com/go-libs/infra/i18n v0.0.3 // indirect
	gitlab.futunn.com/go-libs/infra/inet v0.0.0-20230905031048-271d08fd5b96 // indirect
	gitlab.futunn.com/go-libs/infra/pathconv v0.1.1 // indirect
	gitlab.futunn.com/golang/cmlb v0.6.0 // indirect
	gitlab.futunn.com/golang/env v0.0.2 // indirect
	gitlab.futunn.com/golang/fmonitor v0.3.1 // indirect
	gitlab.futunn.com/golang/ftrace-instrumentation/frpc v1.1.13 // indirect
	gitlab.futunn.com/golang/ftrace-instrumentation/gorm v1.1.9 // indirect
	gitlab.futunn.com/golang/metric v0.0.12 // indirect
	gitlab.futunn.com/golang/monitor v1.0.1 // indirect
	gitlab.futunn.com/golang/pprof v0.0.14 // indirect
	gitlab.futunn.com/golang/srpc v1.1.4 // indirect
	gitlab.futunn.com/infra/fork/mysql v0.0.2 // indirect
	gitlab.futunn.com/infra/futu_auth/service_auth_sdk v1.2.6 // indirect
	gitlab.futunn.com/infra/naming_service/libs/registry v1.0.0 // indirect
	gitlab.futunn.com/infra/naming_service/libs/rule v0.4.0 // indirect
	gitlab.futunn.com/infra/naming_service/libs/tag_build_rule v0.2.0 // indirect
	gitlab.futunn.com/infra/naming_service/sdk/fns_go v1.3.7 // indirect
	go.opentelemetry.io/otel v1.26.0 // indirect
	go.opentelemetry.io/otel/trace v1.26.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/automaxprocs v1.5.1 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/arch v0.0.0-20210923205945-b76863e36670 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sync v0.11.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/datatypes v1.1.1-0.20230130040222-c43177d3cf8c // indirect
	gorm.io/driver/clickhouse v0.5.0 // indirect
	gorm.io/driver/mysql v1.5.1 // indirect
	gorm.io/hints v1.1.2 // indirect
)

replace (
	github.com/mitchellh/mapstructure => gitlab.futunn.com/infra/fork/mapstructure v0.0.3
	github.com/shopspring/decimal => gitlab.futunn.com/infra/fork/decimal v1.3.2
)
