package di

import (
	"context"

	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/biz"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/repo/qtcard"
)

func GetCardPrivilegeBiz(ctx context.Context) *biz.CardPrivilege {
	repoImpl := GetQTCardRepoByCtx(ctx)
	return biz.NewCardPrivilege(repoImpl)
}

func GetQTCardRepoByCtx(ctx context.Context) qtcard.IQTCardRepo {
	readDB := qtcard.NewQtCardDBRead(ctx)
	return qtcard.NewQTCardRepo(readDB)
}
