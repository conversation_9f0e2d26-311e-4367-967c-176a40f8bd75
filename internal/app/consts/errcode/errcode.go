package errcode

import (
	"gitlab.futunn.com/artifact-go/********************service/pb/cardprivilegesyncservice"
	"gitlab.futunn.com/go-libs/infra/errors"
)

var (
	ErrInternalServerError = errors.NewErrServiceInternal(int32(cardprivilegesyncservice.ErrorCode_INTERNAL_SERVER_ERR), "服务内部异常")
	ErrParameterInvalid    = errors.NewErrBadRequest(int32(cardprivilegesyncservice.ErrorCode_PARAMETER_INVALID), "请求参数有误").WithIgnore(true)
	ErrUnknown             = errors.NewErrServiceInternal(int32(cardprivilegesyncservice.ErrorCode_UNKNOWN), "未知异常")
)

var allowErrs = []error{
	ErrInternalServerError,
	ErrParameterInvalid,
}

// ToRspErr 将error类型做收归处理，仅给调用方返回已定义的错误
func ToRspErr(err error) error {
	if err == nil {
		return nil
	}

	for _, allowErr := range allowErrs {
		if errors.Is(err, allowErr) {
			return err
		}
	}

	return ErrUnknown.WithOriginErr(err).WithMessage(err.Error())
}
