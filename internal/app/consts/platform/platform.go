package platform

import (
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/web_data_application/golib/util/location"
)

const (
	NiuNiu = "niuniu"
	MooMoo = "moomoo"
)

var platformMap = map[location.Location]string{
	location.CN: NiuNiu,
	location.HK: NiuNiu,
	location.US: MooMoo,
	location.SG: MooMoo,
	location.JP: MooMoo,
}

func GetPlatform() string {
	loc := location.GetLocation()
	platform, ok := platformMap[loc]
	if !ok {
		return "unknown_platform"
	}
	return platform
}

func IsNiuNiu(clientType uint32) bool {
	return (clientType >= metadata.ClientTypeNNWin && clientType <= metadata.ClientTypeNNShort) ||
		(clientType >= metadata.ClientTypeQTNNWin && clientType <= metadata.ClientTypeQTNNWeb) ||
		(clientType >= metadata.ClientTypeNNGW && clientType <= metadata.ClientTypeNNQuantCloud)
}

func <PERSON><PERSON>oo<PERSON>oo(clientType uint32) bool {
	return (clientType >= metadata.ClientTypeMMWin && clientType <= metadata.ClientTypeMMWeb) ||
		(clientType >= metadata.ClientTypeQTMMWin && clientType <= metadata.ClientTypeQTMMWeb) ||
		(clientType >= metadata.ClientTypeMMGW && clientType <= metadata.ClientTypeMMQuantCloud)
}
