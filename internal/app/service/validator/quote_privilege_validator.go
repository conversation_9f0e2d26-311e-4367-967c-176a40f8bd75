package validator

import (
	"context"

	pb "gitlab.futunn.com/artifact-go/********************service/pb/cardprivilegesyncservice"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/consts/errcode"
)

func ValidateGetQuoteEndTimeByUidReq(ctx context.Context, req *pb.GetQuoteEndTimeByUidReq) error {
	return validateUid(ctx)
}

func ValidateBatchGetQuoteEndTimeReq(ctx context.Context, req *pb.BatchGetQuoteEndTimeReq) error {
	if req.GetPageSize() <= 0 {
		return errcode.ErrParameterInvalid.WithMessage("page size should be greater than 0")
	}
	if req.GetPageSize() > 5000 {
		return errcode.ErrParameterInvalid.WithMessage("page size should be less than 5000")
	}
	return nil
}
