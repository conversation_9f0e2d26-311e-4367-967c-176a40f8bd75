package validator

import (
	"context"

	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/consts/errcode"
	metadatautil "gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/util/metadata"
)

const (
	uidEmptyErrStr = "uid should not be empty"
)

func validateUid(ctx context.Context) error {
	if uid := metadatautil.GetOriginUid(ctx); uid == 0 {
		return errcode.ErrParameterInvalid.WithMessage(uidEmptyErrStr)
	}
	return nil
}
