// Code generated by protoc-gen-pb-impl. PLEASE IMPLEMENT YOUR BUSINESS LOGIC.
// source: card_privilege_sync_service.proto

package cardprivilegesyncservice

import (
	context "context"

	api "gitlab.futunn.com/artifact-go/********************service/api/cardprivilegesyncservice"
	pb "gitlab.futunn.com/artifact-go/********************service/pb/cardprivilegesyncservice"
	_ "gitlab.futunn.com/artifact-go/common/srpc"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/consts/platform"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/di"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/service/validator"
	metadatautil "gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/util/metadata"
	"gitlab.futunn.com/web_data_application/golib/util/location"
	"google.golang.org/protobuf/proto"
)

type cardPrivilegeSyncImpl struct{}

// 注意此模块需要在main.go中注册才能有效
func init() {
	api.RegisterCardPrivilegeSync(NewCardPrivilegeSyncImpl())
}

func NewCardPrivilegeSyncImpl() api.CardPrivilegeSync {
	return cardPrivilegeSyncImpl{}
}

func (e cardPrivilegeSyncImpl) GetQuoteEndTimeByUid(ctx context.Context, request *pb.GetQuoteEndTimeByUidReq, response *pb.GetQuoteEndTimeByUidRsp) error {
	if err := validator.ValidateGetQuoteEndTimeByUidReq(ctx, request); err != nil {
		return err
	}

	biz := di.GetCardPrivilegeBiz(ctx)
	infos, err := biz.GetQuoteEndTimeByUid(ctx, metadatautil.GetOriginUid(ctx), request)
	response.List = infos
	response.PlatformInfo = &pb.PlatformInfo{
		Platform: proto.String(platform.GetPlatform()),
		Location: proto.String(string(location.GetLocation())),
	}

	return err
}

func (e cardPrivilegeSyncImpl) BatchGetQuoteEndTime(ctx context.Context, request *pb.BatchGetQuoteEndTimeReq, response *pb.BatchGetQuoteEndTimeRsp) error {
	if err := validator.ValidateBatchGetQuoteEndTimeReq(ctx, request); err != nil {
		return err
	}

	biz := di.GetCardPrivilegeBiz(ctx)
	quoteEndTimeInfoGroups, nextPageToken, err := biz.BatchGetQuoteEndTime(ctx, request)
	response.QuoteEndTimeInfoGroups = quoteEndTimeInfoGroups
	response.NextPageToken = proto.String(nextPageToken)
	response.PlatformInfo = &pb.PlatformInfo{
		Platform: proto.String(platform.GetPlatform()),
		Location: proto.String(string(location.GetLocation())),
	}

	return err
}
