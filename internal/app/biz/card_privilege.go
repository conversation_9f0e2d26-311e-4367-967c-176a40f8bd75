package biz

import (
	"context"
	"time"

	"github.com/coocood/freecache"
	"github.com/samber/lo"
	pb "gitlab.futunn.com/artifact-go/********************service/pb/cardprivilegesyncservice"
	"gitlab.futunn.com/go-libs/infra/errors"
	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/infra/frpc/pkg/cache"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/consts/errcode"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/db/qtcard"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/dto"
	qtrepo "gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/repo/qtcard"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/util/pagetoken"
	logutil "gitlab.futunn.com/web_data_application/golib/util/log"
	metricutil "gitlab.futunn.com/web_data_application/golib/util/metric"
	"google.golang.org/protobuf/proto"
)

type CardPrivilege struct {
	repo qtrepo.IQTCardRepo
}

func NewCardPrivilege(repo qtrepo.IQTCardRepo) *CardPrivilege {
	return &CardPrivilege{repo: repo}
}

func (c *CardPrivilege) GetQuoteEndTimeByUid(ctx context.Context, uid uint64, req *pb.GetQuoteEndTimeByUidReq) ([]*pb.GetQuoteEndTimeByUidRsp_QuoteEndTimeInfo, error) {
	quoteTypeMapping, err := c.GetQuoteTypeMapping(ctx)
	if err != nil {
		return nil, err
	}

	queryResult, err := c.repo.QueryUserPriEndTimeByUid(ctx, dto.UserPrivilegeListByUidQuery{
		Uid:               uid,
		IncludeExpireInfo: req.GetIncludeExpireInfo(),
		MarketSvrTypes:    c.buildMarketSvrTypes(req.QuoteTypeList, quoteTypeMapping),
	})
	if err != nil {
		logutil.Error(ctx, "QueryUserPriEndTimeByUid_err", fls.ErrorField(err)).WithAlert()
		return nil, errcode.ErrInternalServerError.WithOriginErr(err)
	}
	quoteTypeKeyByMarketSvrType := c.quoteTypeKeyByMarketSvrType(quoteTypeMapping)

	return lo.Map(queryResult.Items, func(item *qtcard.UserPriEndTime, index int) *pb.GetQuoteEndTimeByUidRsp_QuoteEndTimeInfo {
		var quoteType int32
		if quoteTypeModel, ok := quoteTypeKeyByMarketSvrType[dto.MarketSvrTypeItem{Market: item.Market, SvrType: item.SvrType}]; ok {
			quoteType = quoteTypeModel.ID
		} else {
			logutil.Warn(ctx, "QuoteTypeNotFound", fls.Int32("market", item.Market), fls.Int32("svrType", item.SvrType)).WithInc()
		}

		return &pb.GetQuoteEndTimeByUidRsp_QuoteEndTimeInfo{
			QuoteType:       proto.Int32(quoteType),
			ExpireTimestamp: proto.Int64(item.EndTime),
		}
	}), nil
}

func (c *CardPrivilege) BatchGetQuoteEndTime(ctx context.Context, req *pb.BatchGetQuoteEndTimeReq) ([]*pb.QuoteEndTimeInfoGroup, string, error) {
	quoteTypeMapping, err := c.GetQuoteTypeMapping(ctx)
	if err != nil {
		return nil, req.GetPageToken(), err
	}

	queryResult, err := c.repo.QueryUserPriEndTimeSinceLastTime(ctx, dto.UserPriEndTimeSinceLastTimeQuery{
		MarketSvrTypes: c.buildMarketSvrTypes(req.QuoteTypeList, quoteTypeMapping),
		Cursor:         pagetoken.FromPageToken[dto.Cursor](req.GetPageToken()),
		Size:           int(req.GetPageSize()),
	})
	if err != nil {
		logutil.Error(ctx, "QueryUserPriEndTimeSinceLastTime_err", fls.ErrorField(err)).WithAlert()
		return nil, req.GetPageToken(), errcode.ErrInternalServerError.WithOriginErr(err)
	}

	marketSvrType2UserPriEndTime := lo.GroupBy(queryResult.Items, func(item *qtcard.UserPriEndTime) dto.MarketSvrTypeItem {
		return dto.MarketSvrTypeItem{
			Market:  item.Market,
			SvrType: item.SvrType,
		}
	})

	quoteTypeKeyByMarketSvrType := c.quoteTypeKeyByMarketSvrType(quoteTypeMapping)

	return lo.MapToSlice(marketSvrType2UserPriEndTime, func(marketSvrType dto.MarketSvrTypeItem, items []*qtcard.UserPriEndTime) *pb.QuoteEndTimeInfoGroup {
		var quoteType int32
		if quoteTypeModel, ok := quoteTypeKeyByMarketSvrType[marketSvrType]; ok {
			quoteType = quoteTypeModel.ID
		} else {
			logutil.Warn(ctx, "QuoteTypeNotFound", fls.Any("marketSvrType", marketSvrType)).WithInc()
		}

		return &pb.QuoteEndTimeInfoGroup{
			QuoteType: proto.Int32(quoteType),
			Items: lo.Map(items, func(item *qtcard.UserPriEndTime, index int) *pb.QuoteEndTimeInfoGroup_Item {
				return &pb.QuoteEndTimeInfoGroup_Item{
					Uid:             proto.Uint64(uint64(item.UID)),
					ExpireTimestamp: proto.Int64(item.EndTime),
				}
			}),
		}
	}), pagetoken.ToPageToken(queryResult.NextCursor), nil
}

func (c *CardPrivilege) GetQuoteTypeMapping(ctx context.Context) (map[int32]*qtcard.QuoteType, error) {
	cacher, ok := cache.GetCacher("statistic_info_cache")
	if !ok {
		logutil.Error(ctx, "GetCacher_err").WithAlert()
		return c.getQuoteTypeMapping(ctx)
	}

	const cacheKey = "quote_types"
	cacheInfo, err := cacher.Get(ctx, cacheKey)
	if err != nil {
		if errors.Is(err, freecache.ErrNotFound) {
			logutil.Info(ctx, "GetCache_not_found").WithInc()
		} else {
			logutil.Error(ctx, "GetCache_err", fls.ErrorField(err)).WithAlert()
		}

		result, err := c.getQuoteTypeMapping(ctx)
		if err != nil {
			return nil, err
		}
		if err = cacher.SetWithExpiry(ctx, cacheKey, result, 1*time.Minute); err != nil {
			logutil.Error(ctx, "SetCache_err", fls.ErrorField(err)).WithAlert()
		}
		return result, nil
	}
	metricutil.Inc("quote_type_cache_hit")
	return cacheInfo.(map[int32]*qtcard.QuoteType), nil
}

func (c *CardPrivilege) getQuoteTypeMapping(ctx context.Context) (map[int32]*qtcard.QuoteType, error) {
	result, err := c.repo.QueryQuoteTypes(ctx)
	if err != nil {
		logutil.Error(ctx, "QueryQuoteTypeMarketSvrType_err", fls.ErrorField(err)).WithAlert()
		return nil, errcode.ErrInternalServerError.WithOriginErr(err)
	}

	return lo.KeyBy(result, func(item *qtcard.QuoteType) int32 { return item.ID }), nil
}

func (c *CardPrivilege) buildMarketSvrTypes(quoteTypeList []int32, quoteTypeMapping map[int32]*qtcard.QuoteType) dto.MarketSvrTypes {
	var result dto.MarketSvrTypes
	for _, quoteType := range quoteTypeList {
		quotTypeModel, ok := quoteTypeMapping[quoteType]
		if !ok {
			logutil.Warn(context.Background(), "QuoteTypeNotFound", fls.Int32("quoteType", quoteType)).WithAlert()
			continue
		}
		result.Items = append(result.Items, dto.MarketSvrTypeItem{
			Market:  quotTypeModel.Market,
			SvrType: quotTypeModel.SvrType,
		})
	}
	return result
}

func (c *CardPrivilege) quoteTypeKeyByMarketSvrType(quoteTypeMapping map[int32]*qtcard.QuoteType) map[dto.MarketSvrTypeItem]*qtcard.QuoteType {
	result := make(map[dto.MarketSvrTypeItem]*qtcard.QuoteType)
	for _, quoteType := range quoteTypeMapping {
		result[dto.MarketSvrTypeItem{
			Market:  quoteType.Market,
			SvrType: quoteType.SvrType,
		}] = quoteType
	}
	return result
}
