// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package qtcard

const TableNameQuoteType = "quote_type"

// QuoteType mapped from table <quote_type>
type QuoteType struct {
	ID            int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Market        int32  `gorm:"column:market;not null;comment:市场 1-港股 2-美股 4-A股" json:"market"`                         // 市场 1-港股 2-美股 4-A股
	SvrType       int32  `gorm:"column:svr_type;not null;comment:行情权限" json:"svr_type"`                                  // 行情权限
	DescSc        string `gorm:"column:desc_sc;not null;comment:权限描述简体" json:"desc_sc"`                                  // 权限描述简体
	DescTc        string `gorm:"column:desc_tc;not null;comment:权限描述繁体" json:"desc_tc"`                                  // 权限描述繁体
	DescEn        string `gorm:"column:desc_en;not null;comment:权限描述英文" json:"desc_en"`                                  // 权限描述英文
	DescJa        string `gorm:"column:desc_ja;not null;comment:权限描述日文" json:"desc_ja"`                                  // 权限描述日文
	DescInBackend string `gorm:"column:desc_in_backend;not null;comment:管理后台权限描述" json:"desc_in_backend"`                // 管理后台权限描述
	ShowInBackend int32  `gorm:"column:show_in_backend;not null;comment:是否在管理后台展示 0-不展示 1-展示" json:"show_in_backend"`    // 是否在管理后台展示 0-不展示 1-展示
	SortInBackend int32  `gorm:"column:sort_in_backend;not null;comment:后台排序顺序" json:"sort_in_backend"`                  // 后台排序顺序
	NoticeEnabled int32  `gorm:"column:notice_enabled;not null;comment:是否通知 0-不通知 1-通知" json:"notice_enabled"`           // 是否通知 0-不通知 1-通知
	IsOpenAPI     int32  `gorm:"column:is_open_api;not null;comment:是否是open_api权限 0-不是 1-是" json:"is_open_api"`          // 是否是open_api权限 0-不是 1-是
	ModifyEnabled int32  `gorm:"column:modify_enabled;not null;comment:管理后台是否可以修改权限 0-不可修改 1-可修改" json:"modify_enabled"` // 管理后台是否可以修改权限 0-不可修改 1-可修改
	CreatedAt     int32  `gorm:"column:created_at;not null;comment:创建时间" json:"created_at"`                              // 创建时间
	UpdatedAt     int32  `gorm:"column:updated_at;not null;comment:更新时间" json:"updated_at"`                              // 更新时间
}

// TableName QuoteType's table name
func (*QuoteType) TableName() string {
	return TableNameQuoteType
}
