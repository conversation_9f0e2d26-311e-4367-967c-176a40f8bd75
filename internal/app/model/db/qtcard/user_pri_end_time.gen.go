// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package qtcard

const TableNameUserPriEndTime = "user_pri_end_time"

// UserPriEndTime mapped from table <user_pri_end_time>
type UserPriEndTime struct {
	ID        int32 `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UID       int32 `gorm:"column:uid;not null;comment:牛牛号" json:"uid"`               // 牛牛号
	Market    int32 `gorm:"column:market;not null;comment:市场" json:"market"`          // 市场
	SvrType   int32 `gorm:"column:svr_type;not null;comment:svr行情权限" json:"svr_type"` // svr行情权限
	EndTime   int64 `gorm:"column:end_time;not null;comment:行情到期时间" json:"end_time"`  // 行情到期时间
	CreatedAt int32 `gorm:"column:created_at;not null" json:"created_at"`
	UpdatedAt int32 `gorm:"column:updated_at;not null" json:"updated_at"`
}

// TableName UserPriEndTime's table name
func (*UserPriEndTime) TableName() string {
	return TableNameUserPriEndTime
}
