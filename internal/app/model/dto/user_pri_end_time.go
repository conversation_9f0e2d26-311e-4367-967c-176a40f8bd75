package dto

import "gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/db/qtcard"

// UserPrivilegeListByUidQuery 根据uid查询用户权限列表
type UserPrivilegeListByUidQuery struct {
	Uid               uint64
	MarketSvrTypes    MarketSvrTypes
	IncludeExpireInfo bool
}

type UserPrivilegeListByUidQueryResult struct {
	Items []*qtcard.UserPriEndTime
}

// UserPriEndTimeSinceLastTimeQuery 根据更新时间和id增量查询后续数据
type UserPriEndTimeSinceLastTimeQuery struct {
	MarketSvrTypes MarketSvrTypes
	Cursor         Cursor
	Size           int
}

type UserPriEndTimeSinceLastTimeQueryResult struct {
	Items      []*qtcard.UserPriEndTime
	NextCursor Cursor
}

type MarketSvrTypes struct {
	Items []MarketSvrTypeItem
}

type MarketSvrTypeItem struct {
	Market  int32
	SvrType int32
}

func (m MarketSvrTypes) BuildCondition() [][]any {
	var conditions [][]any
	for _, item := range m.Items {
		conditions = append(conditions, []any{item.Market, item.SvrType})
	}
	return conditions
}
