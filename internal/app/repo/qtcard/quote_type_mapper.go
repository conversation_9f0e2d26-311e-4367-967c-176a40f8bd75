package qtcard

import (
	"context"

	"gitlab.futunn.com/go-libs/infra/errors"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/db/qtcard"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/repo/qtcard/gen"
)

type IQuoteTypeMapper interface {
	QueryQuoteTypes(ctx context.Context) ([]*qtcard.QuoteType, error)
}

type quoteTypeMapper struct {
	queryRead *gen.Query
}

func (q *quoteTypeMapper) QueryQuoteTypes(ctx context.Context) ([]*qtcard.QuoteType, error) {
	quoteTypeTable := q.queryRead.QuoteType
	query := q.queryRead.WithContext(ctx).QuoteType

	list, err := query.Select(quoteTypeTable.ID, quoteTypeTable.Market, quoteTypeTable.SvrType).Find()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return list, nil
}
