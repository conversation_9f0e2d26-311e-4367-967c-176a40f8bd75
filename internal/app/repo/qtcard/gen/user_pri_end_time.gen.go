// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/db/qtcard"
)

func newUserPriEndTime(db *gorm.DB, opts ...gen.DOOption) userPriEndTime {
	_userPriEndTime := userPriEndTime{}

	_userPriEndTime.userPriEndTimeDo.UseDB(db, opts...)
	_userPriEndTime.userPriEndTimeDo.UseModel(&qtcard.UserPriEndTime{})

	tableName := _userPriEndTime.userPriEndTimeDo.TableName()
	_userPriEndTime.ALL = field.NewAsterisk(tableName)
	_userPriEndTime.ID = field.NewInt32(tableName, "id")
	_userPriEndTime.UID = field.NewInt32(tableName, "uid")
	_userPriEndTime.Market = field.NewInt32(tableName, "market")
	_userPriEndTime.SvrType = field.NewInt32(tableName, "svr_type")
	_userPriEndTime.EndTime = field.NewInt64(tableName, "end_time")
	_userPriEndTime.CreatedAt = field.NewInt32(tableName, "created_at")
	_userPriEndTime.UpdatedAt = field.NewInt32(tableName, "updated_at")

	_userPriEndTime.fillFieldMap()

	return _userPriEndTime
}

type userPriEndTime struct {
	userPriEndTimeDo userPriEndTimeDo

	ALL       field.Asterisk
	ID        field.Int32
	UID       field.Int32 // 牛牛号
	Market    field.Int32 // 市场
	SvrType   field.Int32 // svr行情权限
	EndTime   field.Int64 // 行情到期时间
	CreatedAt field.Int32
	UpdatedAt field.Int32

	fieldMap map[string]field.Expr
}

func (u userPriEndTime) Table(newTableName string) *userPriEndTime {
	u.userPriEndTimeDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userPriEndTime) As(alias string) *userPriEndTime {
	u.userPriEndTimeDo.DO = *(u.userPriEndTimeDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userPriEndTime) updateTableName(table string) *userPriEndTime {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt32(table, "id")
	u.UID = field.NewInt32(table, "uid")
	u.Market = field.NewInt32(table, "market")
	u.SvrType = field.NewInt32(table, "svr_type")
	u.EndTime = field.NewInt64(table, "end_time")
	u.CreatedAt = field.NewInt32(table, "created_at")
	u.UpdatedAt = field.NewInt32(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userPriEndTime) WithContext(ctx context.Context) *userPriEndTimeDo {
	return u.userPriEndTimeDo.WithContext(ctx)
}

func (u userPriEndTime) TableName() string { return u.userPriEndTimeDo.TableName() }

func (u userPriEndTime) Alias() string { return u.userPriEndTimeDo.Alias() }

func (u userPriEndTime) Columns(cols ...field.Expr) gen.Columns {
	return u.userPriEndTimeDo.Columns(cols...)
}

func (u *userPriEndTime) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userPriEndTime) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 7)
	u.fieldMap["id"] = u.ID
	u.fieldMap["uid"] = u.UID
	u.fieldMap["market"] = u.Market
	u.fieldMap["svr_type"] = u.SvrType
	u.fieldMap["end_time"] = u.EndTime
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userPriEndTime) clone(db *gorm.DB) userPriEndTime {
	u.userPriEndTimeDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userPriEndTime) replaceDB(db *gorm.DB) userPriEndTime {
	u.userPriEndTimeDo.ReplaceDB(db)
	return u
}

type userPriEndTimeDo struct{ gen.DO }

func (u userPriEndTimeDo) Debug() *userPriEndTimeDo {
	return u.withDO(u.DO.Debug())
}

func (u userPriEndTimeDo) WithContext(ctx context.Context) *userPriEndTimeDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userPriEndTimeDo) ReadDB() *userPriEndTimeDo {
	return u.Clauses(dbresolver.Read)
}

func (u userPriEndTimeDo) WriteDB() *userPriEndTimeDo {
	return u.Clauses(dbresolver.Write)
}

func (u userPriEndTimeDo) Session(config *gorm.Session) *userPriEndTimeDo {
	return u.withDO(u.DO.Session(config))
}

func (u userPriEndTimeDo) Clauses(conds ...clause.Expression) *userPriEndTimeDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userPriEndTimeDo) Returning(value interface{}, columns ...string) *userPriEndTimeDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userPriEndTimeDo) Not(conds ...gen.Condition) *userPriEndTimeDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userPriEndTimeDo) Or(conds ...gen.Condition) *userPriEndTimeDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userPriEndTimeDo) Select(conds ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userPriEndTimeDo) Where(conds ...gen.Condition) *userPriEndTimeDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userPriEndTimeDo) Order(conds ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userPriEndTimeDo) Distinct(cols ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userPriEndTimeDo) Omit(cols ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userPriEndTimeDo) Join(table schema.Tabler, on ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userPriEndTimeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userPriEndTimeDo) RightJoin(table schema.Tabler, on ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userPriEndTimeDo) Group(cols ...field.Expr) *userPriEndTimeDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userPriEndTimeDo) Having(conds ...gen.Condition) *userPriEndTimeDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userPriEndTimeDo) Limit(limit int) *userPriEndTimeDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userPriEndTimeDo) Offset(offset int) *userPriEndTimeDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userPriEndTimeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *userPriEndTimeDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userPriEndTimeDo) Unscoped() *userPriEndTimeDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userPriEndTimeDo) Create(values ...*qtcard.UserPriEndTime) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userPriEndTimeDo) CreateInBatches(values []*qtcard.UserPriEndTime, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userPriEndTimeDo) Save(values ...*qtcard.UserPriEndTime) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userPriEndTimeDo) First() (*qtcard.UserPriEndTime, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.UserPriEndTime), nil
	}
}

func (u userPriEndTimeDo) Take() (*qtcard.UserPriEndTime, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.UserPriEndTime), nil
	}
}

func (u userPriEndTimeDo) Last() (*qtcard.UserPriEndTime, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.UserPriEndTime), nil
	}
}

func (u userPriEndTimeDo) Find() ([]*qtcard.UserPriEndTime, error) {
	result, err := u.DO.Find()
	return result.([]*qtcard.UserPriEndTime), err
}

func (u userPriEndTimeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*qtcard.UserPriEndTime, err error) {
	buf := make([]*qtcard.UserPriEndTime, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userPriEndTimeDo) FindInBatches(result *[]*qtcard.UserPriEndTime, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userPriEndTimeDo) Attrs(attrs ...field.AssignExpr) *userPriEndTimeDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userPriEndTimeDo) Assign(attrs ...field.AssignExpr) *userPriEndTimeDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userPriEndTimeDo) Joins(fields ...field.RelationField) *userPriEndTimeDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userPriEndTimeDo) Preload(fields ...field.RelationField) *userPriEndTimeDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userPriEndTimeDo) FirstOrInit() (*qtcard.UserPriEndTime, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.UserPriEndTime), nil
	}
}

func (u userPriEndTimeDo) FirstOrCreate() (*qtcard.UserPriEndTime, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.UserPriEndTime), nil
	}
}

func (u userPriEndTimeDo) FindByPage(offset int, limit int) (result []*qtcard.UserPriEndTime, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userPriEndTimeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userPriEndTimeDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userPriEndTimeDo) Delete(models ...*qtcard.UserPriEndTime) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userPriEndTimeDo) withDO(do gen.Dao) *userPriEndTimeDo {
	u.DO = *do.(*gen.DO)
	return u
}
