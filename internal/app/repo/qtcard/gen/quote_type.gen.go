// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/db/qtcard"
)

func newQuoteType(db *gorm.DB, opts ...gen.DOOption) quoteType {
	_quoteType := quoteType{}

	_quoteType.quoteTypeDo.UseDB(db, opts...)
	_quoteType.quoteTypeDo.UseModel(&qtcard.QuoteType{})

	tableName := _quoteType.quoteTypeDo.TableName()
	_quoteType.ALL = field.NewAsterisk(tableName)
	_quoteType.ID = field.NewInt32(tableName, "id")
	_quoteType.Market = field.NewInt32(tableName, "market")
	_quoteType.SvrType = field.NewInt32(tableName, "svr_type")
	_quoteType.DescSc = field.NewString(tableName, "desc_sc")
	_quoteType.DescTc = field.NewString(tableName, "desc_tc")
	_quoteType.DescEn = field.NewString(tableName, "desc_en")
	_quoteType.DescJa = field.NewString(tableName, "desc_ja")
	_quoteType.DescInBackend = field.NewString(tableName, "desc_in_backend")
	_quoteType.ShowInBackend = field.NewInt32(tableName, "show_in_backend")
	_quoteType.SortInBackend = field.NewInt32(tableName, "sort_in_backend")
	_quoteType.NoticeEnabled = field.NewInt32(tableName, "notice_enabled")
	_quoteType.IsOpenAPI = field.NewInt32(tableName, "is_open_api")
	_quoteType.ModifyEnabled = field.NewInt32(tableName, "modify_enabled")
	_quoteType.CreatedAt = field.NewInt32(tableName, "created_at")
	_quoteType.UpdatedAt = field.NewInt32(tableName, "updated_at")

	_quoteType.fillFieldMap()

	return _quoteType
}

type quoteType struct {
	quoteTypeDo quoteTypeDo

	ALL           field.Asterisk
	ID            field.Int32
	Market        field.Int32  // 市场 1-港股 2-美股 4-A股
	SvrType       field.Int32  // 行情权限
	DescSc        field.String // 权限描述简体
	DescTc        field.String // 权限描述繁体
	DescEn        field.String // 权限描述英文
	DescJa        field.String // 权限描述日文
	DescInBackend field.String // 管理后台权限描述
	ShowInBackend field.Int32  // 是否在管理后台展示 0-不展示 1-展示
	SortInBackend field.Int32  // 后台排序顺序
	NoticeEnabled field.Int32  // 是否通知 0-不通知 1-通知
	IsOpenAPI     field.Int32  // 是否是open_api权限 0-不是 1-是
	ModifyEnabled field.Int32  // 管理后台是否可以修改权限 0-不可修改 1-可修改
	CreatedAt     field.Int32  // 创建时间
	UpdatedAt     field.Int32  // 更新时间

	fieldMap map[string]field.Expr
}

func (q quoteType) Table(newTableName string) *quoteType {
	q.quoteTypeDo.UseTable(newTableName)
	return q.updateTableName(newTableName)
}

func (q quoteType) As(alias string) *quoteType {
	q.quoteTypeDo.DO = *(q.quoteTypeDo.As(alias).(*gen.DO))
	return q.updateTableName(alias)
}

func (q *quoteType) updateTableName(table string) *quoteType {
	q.ALL = field.NewAsterisk(table)
	q.ID = field.NewInt32(table, "id")
	q.Market = field.NewInt32(table, "market")
	q.SvrType = field.NewInt32(table, "svr_type")
	q.DescSc = field.NewString(table, "desc_sc")
	q.DescTc = field.NewString(table, "desc_tc")
	q.DescEn = field.NewString(table, "desc_en")
	q.DescJa = field.NewString(table, "desc_ja")
	q.DescInBackend = field.NewString(table, "desc_in_backend")
	q.ShowInBackend = field.NewInt32(table, "show_in_backend")
	q.SortInBackend = field.NewInt32(table, "sort_in_backend")
	q.NoticeEnabled = field.NewInt32(table, "notice_enabled")
	q.IsOpenAPI = field.NewInt32(table, "is_open_api")
	q.ModifyEnabled = field.NewInt32(table, "modify_enabled")
	q.CreatedAt = field.NewInt32(table, "created_at")
	q.UpdatedAt = field.NewInt32(table, "updated_at")

	q.fillFieldMap()

	return q
}

func (q *quoteType) WithContext(ctx context.Context) *quoteTypeDo {
	return q.quoteTypeDo.WithContext(ctx)
}

func (q quoteType) TableName() string { return q.quoteTypeDo.TableName() }

func (q quoteType) Alias() string { return q.quoteTypeDo.Alias() }

func (q quoteType) Columns(cols ...field.Expr) gen.Columns { return q.quoteTypeDo.Columns(cols...) }

func (q *quoteType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := q.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (q *quoteType) fillFieldMap() {
	q.fieldMap = make(map[string]field.Expr, 15)
	q.fieldMap["id"] = q.ID
	q.fieldMap["market"] = q.Market
	q.fieldMap["svr_type"] = q.SvrType
	q.fieldMap["desc_sc"] = q.DescSc
	q.fieldMap["desc_tc"] = q.DescTc
	q.fieldMap["desc_en"] = q.DescEn
	q.fieldMap["desc_ja"] = q.DescJa
	q.fieldMap["desc_in_backend"] = q.DescInBackend
	q.fieldMap["show_in_backend"] = q.ShowInBackend
	q.fieldMap["sort_in_backend"] = q.SortInBackend
	q.fieldMap["notice_enabled"] = q.NoticeEnabled
	q.fieldMap["is_open_api"] = q.IsOpenAPI
	q.fieldMap["modify_enabled"] = q.ModifyEnabled
	q.fieldMap["created_at"] = q.CreatedAt
	q.fieldMap["updated_at"] = q.UpdatedAt
}

func (q quoteType) clone(db *gorm.DB) quoteType {
	q.quoteTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return q
}

func (q quoteType) replaceDB(db *gorm.DB) quoteType {
	q.quoteTypeDo.ReplaceDB(db)
	return q
}

type quoteTypeDo struct{ gen.DO }

func (q quoteTypeDo) Debug() *quoteTypeDo {
	return q.withDO(q.DO.Debug())
}

func (q quoteTypeDo) WithContext(ctx context.Context) *quoteTypeDo {
	return q.withDO(q.DO.WithContext(ctx))
}

func (q quoteTypeDo) ReadDB() *quoteTypeDo {
	return q.Clauses(dbresolver.Read)
}

func (q quoteTypeDo) WriteDB() *quoteTypeDo {
	return q.Clauses(dbresolver.Write)
}

func (q quoteTypeDo) Session(config *gorm.Session) *quoteTypeDo {
	return q.withDO(q.DO.Session(config))
}

func (q quoteTypeDo) Clauses(conds ...clause.Expression) *quoteTypeDo {
	return q.withDO(q.DO.Clauses(conds...))
}

func (q quoteTypeDo) Returning(value interface{}, columns ...string) *quoteTypeDo {
	return q.withDO(q.DO.Returning(value, columns...))
}

func (q quoteTypeDo) Not(conds ...gen.Condition) *quoteTypeDo {
	return q.withDO(q.DO.Not(conds...))
}

func (q quoteTypeDo) Or(conds ...gen.Condition) *quoteTypeDo {
	return q.withDO(q.DO.Or(conds...))
}

func (q quoteTypeDo) Select(conds ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.Select(conds...))
}

func (q quoteTypeDo) Where(conds ...gen.Condition) *quoteTypeDo {
	return q.withDO(q.DO.Where(conds...))
}

func (q quoteTypeDo) Order(conds ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.Order(conds...))
}

func (q quoteTypeDo) Distinct(cols ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.Distinct(cols...))
}

func (q quoteTypeDo) Omit(cols ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.Omit(cols...))
}

func (q quoteTypeDo) Join(table schema.Tabler, on ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.Join(table, on...))
}

func (q quoteTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.LeftJoin(table, on...))
}

func (q quoteTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.RightJoin(table, on...))
}

func (q quoteTypeDo) Group(cols ...field.Expr) *quoteTypeDo {
	return q.withDO(q.DO.Group(cols...))
}

func (q quoteTypeDo) Having(conds ...gen.Condition) *quoteTypeDo {
	return q.withDO(q.DO.Having(conds...))
}

func (q quoteTypeDo) Limit(limit int) *quoteTypeDo {
	return q.withDO(q.DO.Limit(limit))
}

func (q quoteTypeDo) Offset(offset int) *quoteTypeDo {
	return q.withDO(q.DO.Offset(offset))
}

func (q quoteTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *quoteTypeDo {
	return q.withDO(q.DO.Scopes(funcs...))
}

func (q quoteTypeDo) Unscoped() *quoteTypeDo {
	return q.withDO(q.DO.Unscoped())
}

func (q quoteTypeDo) Create(values ...*qtcard.QuoteType) error {
	if len(values) == 0 {
		return nil
	}
	return q.DO.Create(values)
}

func (q quoteTypeDo) CreateInBatches(values []*qtcard.QuoteType, batchSize int) error {
	return q.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (q quoteTypeDo) Save(values ...*qtcard.QuoteType) error {
	if len(values) == 0 {
		return nil
	}
	return q.DO.Save(values)
}

func (q quoteTypeDo) First() (*qtcard.QuoteType, error) {
	if result, err := q.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.QuoteType), nil
	}
}

func (q quoteTypeDo) Take() (*qtcard.QuoteType, error) {
	if result, err := q.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.QuoteType), nil
	}
}

func (q quoteTypeDo) Last() (*qtcard.QuoteType, error) {
	if result, err := q.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.QuoteType), nil
	}
}

func (q quoteTypeDo) Find() ([]*qtcard.QuoteType, error) {
	result, err := q.DO.Find()
	return result.([]*qtcard.QuoteType), err
}

func (q quoteTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*qtcard.QuoteType, err error) {
	buf := make([]*qtcard.QuoteType, 0, batchSize)
	err = q.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (q quoteTypeDo) FindInBatches(result *[]*qtcard.QuoteType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return q.DO.FindInBatches(result, batchSize, fc)
}

func (q quoteTypeDo) Attrs(attrs ...field.AssignExpr) *quoteTypeDo {
	return q.withDO(q.DO.Attrs(attrs...))
}

func (q quoteTypeDo) Assign(attrs ...field.AssignExpr) *quoteTypeDo {
	return q.withDO(q.DO.Assign(attrs...))
}

func (q quoteTypeDo) Joins(fields ...field.RelationField) *quoteTypeDo {
	for _, _f := range fields {
		q = *q.withDO(q.DO.Joins(_f))
	}
	return &q
}

func (q quoteTypeDo) Preload(fields ...field.RelationField) *quoteTypeDo {
	for _, _f := range fields {
		q = *q.withDO(q.DO.Preload(_f))
	}
	return &q
}

func (q quoteTypeDo) FirstOrInit() (*qtcard.QuoteType, error) {
	if result, err := q.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.QuoteType), nil
	}
}

func (q quoteTypeDo) FirstOrCreate() (*qtcard.QuoteType, error) {
	if result, err := q.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*qtcard.QuoteType), nil
	}
}

func (q quoteTypeDo) FindByPage(offset int, limit int) (result []*qtcard.QuoteType, count int64, err error) {
	result, err = q.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = q.Offset(-1).Limit(-1).Count()
	return
}

func (q quoteTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = q.Count()
	if err != nil {
		return
	}

	err = q.Offset(offset).Limit(limit).Scan(result)
	return
}

func (q quoteTypeDo) Scan(result interface{}) (err error) {
	return q.DO.Scan(result)
}

func (q quoteTypeDo) Delete(models ...*qtcard.QuoteType) (result gen.ResultInfo, err error) {
	return q.DO.Delete(models)
}

func (q *quoteTypeDo) withDO(do gen.Dao) *quoteTypeDo {
	q.DO = *do.(*gen.DO)
	return q
}
