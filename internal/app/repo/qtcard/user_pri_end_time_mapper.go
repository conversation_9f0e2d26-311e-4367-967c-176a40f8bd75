package qtcard

import (
	"context"
	"time"

	"gitlab.futunn.com/go-libs/infra/errors"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/model/dto"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/repo/qtcard/gen"
	"gorm.io/gen/field"
)

type IUserPriEndTimeMapper interface {
	QueryUserPriEndTimeByUid(ctx context.Context, queryInfo dto.UserPrivilegeListByUidQuery) (*dto.UserPrivilegeListByUidQueryResult, error)
	QueryUserPriEndTimeSinceLastTime(ctx context.Context, queryInfo dto.UserPriEndTimeSinceLastTimeQuery) (*dto.UserPriEndTimeSinceLastTimeQueryResult, error)
}

type userPriEndTimeMapper struct {
	queryRead *gen.Query
}

func (u userPriEndTimeMapper) QueryUserPriEndTimeByUid(ctx context.Context, queryInfo dto.UserPrivilegeListByUidQuery) (*dto.UserPrivilegeListByUidQueryResult, error) {
	userPriEndTimeTable := u.queryRead.UserPriEndTime

	query := u.queryRead.WithContext(ctx).UserPriEndTime
	query = query.Where(userPriEndTimeTable.UID.Eq(int32(queryInfo.Uid)))

	// 过滤已过期的权限记录
	if !queryInfo.IncludeExpireInfo {
		query = query.Where(userPriEndTimeTable.EndTime.Gt(time.Now().Unix()))
	}
	// 过滤指定的权限类型
	if len(queryInfo.MarketSvrTypes.Items) > 0 {
		query = query.Where(userPriEndTimeTable.Columns(userPriEndTimeTable.Market, userPriEndTimeTable.SvrType).In(field.Values(queryInfo.MarketSvrTypes.BuildCondition())))
	}
	query = query.Select(userPriEndTimeTable.Market, userPriEndTimeTable.SvrType, userPriEndTimeTable.EndTime)

	items, err := query.Find()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &dto.UserPrivilegeListByUidQueryResult{
		Items: items,
	}, nil
}

func (u userPriEndTimeMapper) QueryUserPriEndTimeSinceLastTime(ctx context.Context, queryInfo dto.UserPriEndTimeSinceLastTimeQuery) (*dto.UserPriEndTimeSinceLastTimeQueryResult, error) {
	userPriEndTimeTable := u.queryRead.UserPriEndTime

	query := u.queryRead.WithContext(ctx).UserPriEndTime
	innerQuery := u.queryRead.WithContext(ctx).UserPriEndTime

	query = query.Where(
		innerQuery.Where(
			userPriEndTimeTable.UpdatedAt.Gt(int32(queryInfo.Cursor.LastUpdatedAt)),
		).Or(
			userPriEndTimeTable.UpdatedAt.Eq(int32(queryInfo.Cursor.LastUpdatedAt)),
			userPriEndTimeTable.ID.Gt(int32(queryInfo.Cursor.LastID)),
		),
	)

	// 过滤指定的权限类型
	if len(queryInfo.MarketSvrTypes.Items) > 0 {
		query = query.Where(userPriEndTimeTable.Columns(userPriEndTimeTable.Market, userPriEndTimeTable.SvrType).In(field.Values(queryInfo.MarketSvrTypes.BuildCondition())))
	}

	query = query.Limit(queryInfo.Size).Order(userPriEndTimeTable.UpdatedAt.Asc(), userPriEndTimeTable.ID.Asc())
	query = query.Select(
		userPriEndTimeTable.ID,
		userPriEndTimeTable.UID,
		userPriEndTimeTable.Market,
		userPriEndTimeTable.SvrType,
		userPriEndTimeTable.EndTime,
		userPriEndTimeTable.UpdatedAt,
	)

	items, err := query.Find()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	nextCursor := queryInfo.Cursor
	if len(items) > 0 {
		nextCursor.LastID = int64(items[len(items)-1].ID)
		nextCursor.LastUpdatedAt = int64(items[len(items)-1].UpdatedAt)
	}

	return &dto.UserPriEndTimeSinceLastTimeQueryResult{
		Items:      items,
		NextCursor: nextCursor,
	}, nil
}
