package qtcard

import (
	"context"

	"gitlab.futunn.com/infra/frpc/pkg/errors"
	"gitlab.futunn.com/infra/frpc/pkg/thirdparty/db/gorm"
	errutil "gitlab.futunn.com/infra/frpc/pkg/util/error"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/repo/qtcard/gen"
	logutil "gitlab.futunn.com/web_data_application/golib/util/log"
)

type IQTCardRepo interface {
	Tx(ctx context.Context, fn func(context.Context, IQTCardRepo) error) error
	IsRecordNotFound(err error) bool
	IsDuplicatedKey(err error) bool

	IUserPriEndTimeMapper
	IQuoteTypeMapper
}

type qtCardRepoImpl struct {
	query *gen.Query
	isTx  bool

	userPriEndTimeMapper
	quoteTypeMapper
}

func NewQtCardDBRead(ctx context.Context) *gorm.DB {
	db, _ := gorm.NewDB(ctx, "qt_card_read")
	return db
}

func NewQTCardRepo(db *gorm.DB) IQTCardRepo {
	query := gen.Use(db)
	return newRepo(query, false)
}

func newRepo(query *gen.Query, isTx bool) IQTCardRepo {
	return &qtCardRepoImpl{
		query:                query,
		isTx:                 isTx,
		userPriEndTimeMapper: userPriEndTimeMapper{queryRead: query},
		quoteTypeMapper:      quoteTypeMapper{queryRead: query},
	}
}

func (q *qtCardRepoImpl) IsRecordNotFound(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

func (q *qtCardRepoImpl) IsDuplicatedKey(err error) bool {
	return errors.Is(err, gorm.ErrDuplicatedKey) || errutil.IsDuplicateKeyError(err)
}

func (q *qtCardRepoImpl) Tx(ctx context.Context, fn func(context.Context, IQTCardRepo) error) error {
	// q已经在事务上下文中了
	if q.isTx {
		logutil.Warn(ctx, "qt_card: already in transaction").WithInc()
		return fn(ctx, q)
	}

	return q.query.Transaction(func(tx *gen.Query) error {
		txRepo := newRepo(tx, true)
		return fn(ctx, txRepo)
	})
}
