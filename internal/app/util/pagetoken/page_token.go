package pagetoken

import (
	"context"
	"encoding/base64"
	"encoding/json"

	"gitlab.futunn.com/golang/fls"
	logutil "gitlab.futunn.com/web_data_application/golib/util/log"
)

func ToPageToken[T any](cursor T) string {
	bytes, err := json.Marshal(cursor)
	if err != nil {
		logutil.Error(context.Background(), "marshal cursor failed",
			fls.Any("cursor", cursor),
			fls.<PERSON>rror<PERSON>ield(err),
		).WithAlert()
		return ""
	}
	if string(bytes) == "null" {
		return ""
	}

	return base64.StdEncoding.EncodeToString(bytes)
}

func FromPageToken[T any](pageToken string) T {
	var (
		zero   T
		result T
	)

	if pageToken == "" {
		return zero
	}

	data, err := base64.StdEncoding.DecodeString(pageToken)
	if err != nil {
		logutil.Error(context.Background(), "decode cursor failed",
			fls.<PERSON>rror<PERSON>ield(err),
			fls.String("pageToken", pageToken),
		)
		return zero
	}

	if err = json.Unmarshal(data, &result); err != nil {
		logutil.Error(context.Background(), "unmarshal cursor failed",
			fls.ErrorField(err),
			fls.String("pageToken", pageToken),
		)
		return zero
	}

	return result
}
