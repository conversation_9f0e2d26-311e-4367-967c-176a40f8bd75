package pagetoken

import (
	"reflect"
	"testing"
)

func TestToPageToken(t *testing.T) {
	type cursor struct {
		LastID        int64 `json:"last_id"`
		LastUpdatedAt int64 `json:"last_updated_at"`
	}
	type args[T any] struct {
		cursor T
	}
	type testCase[T any] struct {
		name string
		args args[T]
		want string
	}
	tests := []testCase[cursor]{
		{
			name: "case1: cursor为空",
			args: args[cursor]{
				cursor: cursor{},
			},
			want: "eyJsYXN0X2lkIjowLCJsYXN0X3VwZGF0ZWRfYXQiOjB9",
		},
		{
			name: "case1: cursor不为空",
			args: args[cursor]{
				cursor: cursor{LastID: 666, LastUpdatedAt: 888},
			},
			want: "eyJsYXN0X2lkIjo2NjYsImxhc3RfdXBkYXRlZF9hdCI6ODg4fQ==",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToPageToken(tt.args.cursor); got != tt.want {
				t.Errorf("ToPageToken() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFromPageToken(t *testing.T) {
	type cursor struct {
		LastID        int64 `json:"last_id"`
		LastUpdatedAt int64 `json:"last_updated_at"`
	}
	type args struct {
		pageToken string
	}
	type testCase[T any] struct {
		name string
		args args
		want T
	}
	tests := []testCase[cursor]{
		{
			name: "case1: pageToken为空",
			args: args{
				pageToken: "",
			},
			want: cursor{},
		},
		{
			name: "case1: pageToken无效",
			args: args{
				pageToken: "111222333",
			},
			want: cursor{},
		},
		{
			name: "case1: pageToken有效",
			args: args{
				pageToken: "eyJsYXN0X2lkIjo2NjYsImxhc3RfdXBkYXRlZF9hdCI6ODg4fQ==",
			},
			want: cursor{LastID: 666, LastUpdatedAt: 888},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FromPageToken[cursor](tt.args.pageToken); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FromPageToken() = %v, want %v", got, tt.want)
			}
		})
	}
}
