package middleware

import (
	"context"

	"github.com/go-kit/kit/endpoint"
)

type ForwardFunc endpoint.Endpoint

type ForwardStrategy interface {
	NeedForward(ctx context.Context, request interface{}) (bool, ForwardFunc, error)
}

func RequestAutoForward(forwardStrategy ForwardStrategy) endpoint.Middleware {
	return func(next endpoint.Endpoint) endpoint.Endpoint {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			needForward, forwardFunc, err := forwardStrategy.NeedForward(ctx, request)
			if err != nil {
				return nil, err
			}
			if needForward {
				return forwardFunc(ctx, request)
			}
			return next(ctx, request)
		}
	}
}
