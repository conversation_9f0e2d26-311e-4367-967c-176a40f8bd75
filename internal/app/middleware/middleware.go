package middleware

import (
	"gitlab.futunn.com/infra/frpc/pkg/middleware"
	"gitlab.futunn.com/web_data_application/golib/middleware/enhancelog"
)

func init() {
	middleware.AddServerFilter<PERSON>hain(RequestAutoForward(
		new(multiPlatformForwardStrategy),
	))
	middleware.AddServerFilterChain(enhancelog.EnhanceLog(
		enhancelog.WithClientIP(),
		enhancelog.WithClientType(),
		enhancelog.WithClientVersion(),
		enhancelog.WithClientLang(),
		enhancelog.WithUserAttribution(),
		enhancelog.WithGrayFlag(),
		enhancelog.WithCallerName(),
	))
}
