package middleware

import (
	"context"
	"strconv"

	"gitlab.futunn.com/web_data_application/golib/util/metric"

	api "gitlab.futunn.com/artifact-go/********************service/api/cardprivilegesyncservice"
	pb "gitlab.futunn.com/artifact-go/********************service/pb/cardprivilegesyncservice"
	userattributionapi "gitlab.futunn.com/artifact-go/user_attribution/api/userattribution"
	userattributionpb "gitlab.futunn.com/artifact-go/user_attribution/pb/userattribution"
	"gitlab.futunn.com/go-libs/infra/metadata/v2"
	"gitlab.futunn.com/golang/fls"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/consts/errcode"
	"gitlab.futunn.com/web_data_application/card_privilege_sync_service/internal/app/consts/platform"
	"gitlab.futunn.com/web_data_application/golib/util/location"
	logutil "gitlab.futunn.com/web_data_application/golib/util/log"
	"google.golang.org/protobuf/proto"
)

type multiPlatformForwardStrategy struct{}

type sourceEntry struct {
	platform        string
	userAttribution userattributionpb.FtUserRegion
}

type targetEntry struct {
	location        location.Location
	userAttribution userattributionpb.FtUserRegion
}

const (
	forceHandleFlag  = "force_handle_flag"
	forceHandleValue = "card_privilege_sync_service"
)

var source2target = map[sourceEntry]targetEntry{
	// 正常情况下，用户归属地与平台的对应关系
	{platform: platform.NiuNiu, userAttribution: userattributionpb.FtUserRegion_CNRegion}:  {location: location.CN, userAttribution: userattributionpb.FtUserRegion_CNRegion},
	{platform: platform.NiuNiu, userAttribution: userattributionpb.FtUserRegion_HKGRegion}: {location: location.HK, userAttribution: userattributionpb.FtUserRegion_HKGRegion},
	{platform: platform.MooMoo, userAttribution: userattributionpb.FtUserRegion_USRegion}:  {location: location.US, userAttribution: userattributionpb.FtUserRegion_USRegion},
	{platform: platform.MooMoo, userAttribution: userattributionpb.FtUserRegion_SGRegion}:  {location: location.SG, userAttribution: userattributionpb.FtUserRegion_SGRegion},
	{platform: platform.MooMoo, userAttribution: userattributionpb.FtUserRegion_AURegion}:  {location: location.SG, userAttribution: userattributionpb.FtUserRegion_AURegion},
	{platform: platform.MooMoo, userAttribution: userattributionpb.FtUserRegion_JPRegion}:  {location: location.JP, userAttribution: userattributionpb.FtUserRegion_JPRegion},
	// 互登用户归属地与平台的对应关系
	{platform: platform.MooMoo, userAttribution: userattributionpb.FtUserRegion_CNRegion}:  {location: location.US, userAttribution: userattributionpb.FtUserRegion_SGRegion}, // sg实际是us的从库
	{platform: platform.MooMoo, userAttribution: userattributionpb.FtUserRegion_HKGRegion}: {location: location.US, userAttribution: userattributionpb.FtUserRegion_SGRegion}, // hk的用户不能互等，该case在生产环境实际不存在，为了完备性也加上
	{platform: platform.NiuNiu, userAttribution: userattributionpb.FtUserRegion_USRegion}:  {location: location.CN, userAttribution: userattributionpb.FtUserRegion_CNRegion},
	{platform: platform.NiuNiu, userAttribution: userattributionpb.FtUserRegion_SGRegion}:  {location: location.CN, userAttribution: userattributionpb.FtUserRegion_CNRegion},
	{platform: platform.NiuNiu, userAttribution: userattributionpb.FtUserRegion_AURegion}:  {location: location.CN, userAttribution: userattributionpb.FtUserRegion_CNRegion},
	{platform: platform.NiuNiu, userAttribution: userattributionpb.FtUserRegion_JPRegion}:  {location: location.CN, userAttribution: userattributionpb.FtUserRegion_CNRegion}, // jp的用户不能互等，该case在生产环境实际不存在，为了完备性也加上
}

// 非互登情况下，归属地与平台的对应关系
var defaultUserAttribution2Platform = [...]string{
	userattributionpb.FtUserRegion_CNRegion:  platform.NiuNiu,
	userattributionpb.FtUserRegion_USRegion:  platform.MooMoo,
	userattributionpb.FtUserRegion_SGRegion:  platform.MooMoo,
	userattributionpb.FtUserRegion_AURegion:  platform.MooMoo,
	userattributionpb.FtUserRegion_JPRegion:  platform.MooMoo,
	userattributionpb.FtUserRegion_HKGRegion: platform.NiuNiu,
}

func (p *multiPlatformForwardStrategy) NeedForward(ctx context.Context, _ interface{}) (bool, ForwardFunc, error) {
	var (
		clientType, _    = metadata.GetClientType(ctx)
		clientVersion, _ = metadata.GetClientVersion(ctx)
	)

	if v, ok := metadata.GetCustomizedHeader(ctx, forceHandleFlag); ok && v == forceHandleValue {
		logutil.Debug(ctx, "exist_force_handle_flag",
			fls.Uint32("client_type", clientType),
			fls.Uint32("client_version", clientVersion),
			fls.String("location", string(location.GetLocation())),
		).WithInc()
		return false, nil, nil
	}

	requestPlatform, err := p.getRequestPlatform(ctx)
	if err != nil {
		return false, nil, err
	}

	userAttribution, err := p.getUserAttribution(ctx)
	if err != nil {
		return false, nil, err
	}

	srcEntry := sourceEntry{platform: requestPlatform, userAttribution: userAttribution}
	tgtEntry, ok := source2target[srcEntry]
	if !ok {
		logutil.Error(ctx, "unknown_source_entry",
			fls.String("platform", requestPlatform),
			fls.String("userAttribution", userAttribution.String()),
		).WithAlert()
		return false, nil, errcode.ErrInternalServerError.WithMessagef("unknown source entry: %+v", srcEntry)
	}

	// 已经是目标地域，不需要转发
	if location.GetLocation() == tgtEntry.location {
		return false, nil, nil
	}

	// 区分相同平台转发和跨平台转发
	msg := "forward_user_attribution"
	defaultPlatform := defaultUserAttribution2Platform[userAttribution]
	if requestPlatform != defaultPlatform {
		msg = "forward_user_attribution_cross_platform"
	}
	logutil.Info(ctx, msg,
		fls.Uint32("client_type", clientType),
		fls.Uint32("client_version", clientVersion),
		fls.String("user_default_platform", defaultPlatform),
		fls.String("user_request_platform", requestPlatform),
		fls.String("user_attribution", userAttribution.String()),
	).WithInc(
		metric.KV{Key: "client_type", Value: strconv.Itoa(int(clientType))},
		metric.KV{Key: "client_version", Value: strconv.Itoa(int(clientVersion))},
		metric.KV{Key: "user_default_platform", Value: defaultPlatform},
		metric.KV{Key: "user_request_platform", Value: requestPlatform},
		metric.KV{Key: "user_attribution", Value: userAttribution.String()},
	)

	return true, p.getForwardFunc(tgtEntry.userAttribution), nil
}

func (p *multiPlatformForwardStrategy) getRequestPlatform(ctx context.Context) (string, error) {
	clientType, ok := metadata.GetClientType(ctx)
	if !ok {
		return "", errcode.ErrParameterInvalid.WithMessage("client_type should not be empty")
	}

	if platform.IsNiuNiu(clientType) {
		return platform.NiuNiu, nil
	} else if platform.IsMooMoo(clientType) {
		return platform.MooMoo, nil
	}
	return "", errcode.ErrParameterInvalid.WithMessagef("unknown client_type: %d", clientType)
}

func (p *multiPlatformForwardStrategy) getUserAttribution(ctx context.Context) (userattributionpb.FtUserRegion, error) {
	if uid, ok := metadata.GetOriginUid(ctx); ok && uid != 0 {
		rsp, err := userattributionapi.RegistAttribution(ctx, &userattributionpb.RegistAttributionReq{
			Uid: proto.Uint64(uid),
		})
		if err != nil {
			logutil.Error(ctx, "failed_to_regist_attribution",
				fls.Uint64("uid", uid),
				fls.ErrorField(err),
			).WithAlert()
			return 0, errcode.ErrInternalServerError.WithMessage("failed to regist attribution")
		}
		if rsp.GetRetCode() != 0 {
			logutil.Error(ctx, "failed_to_regist_attribution",
				fls.Uint64("uid", uid),
				fls.Int32("retCode", rsp.GetRetCode()),
				fls.String("retMsg", string(rsp.GetRetMsg())),
			).WithAlert()
			return 0, errcode.ErrInternalServerError.WithMessage("failed to regist attribution")
		}

		return userattributionpb.FtUserRegion(rsp.GetRegistArea().GetRegistArea()), nil
	}

	if userAttribution, ok := metadata.GetUserAttribution(ctx); ok && userAttribution != 0 {
		return userattributionpb.FtUserRegion(userAttribution), nil
	}

	return 0, errcode.ErrParameterInvalid.WithMessage("uid or user_attribution should not be empty")
}

func (p *multiPlatformForwardStrategy) getForwardFunc(targetUserAttribution userattributionpb.FtUserRegion) ForwardFunc {
	return func(ctx context.Context, request interface{}) (interface{}, error) {
		ctx = metadata.WithOptionsContext(
			ctx,
			metadata.SetUserAttribution(uint32(targetUserAttribution)),
			metadata.SetCustomizedHeader(forceHandleFlag, forceHandleValue),
		)

		switch r := request.(type) {
		case *pb.GetQuoteEndTimeByUidReq:
			return api.GetQuoteEndTimeByUid(ctx, r)
		case *pb.BatchGetQuoteEndTimeReq:
			return api.BatchGetQuoteEndTime(ctx, r)
		default:
			return nil, errcode.ErrParameterInvalid.WithMessagef("unknown request id: %v", request)
		}
	}
}
