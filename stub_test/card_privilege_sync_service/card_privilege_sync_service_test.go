// Code generated by protoc-gen-client-stub. PLEASE SUPPLEMENT YOUR REQUEST.
// source: card_privilege_sync_service.proto

package card_privilege_sync_service

import (
	context "context"
	fmt "fmt"
	assert "github.com/stretchr/testify/assert"
	cli "gitlab.futunn.com/artifact-go/********************service/api/cardprivilegesyncservice"
	pb "gitlab.futunn.com/artifact-go/********************service/pb/cardprivilegesyncservice"
	_ "gitlab.futunn.com/artifact-go/common/srpc"
	appinfo "gitlab.futunn.com/go-libs/infra/appinfo"
	ftrace "gitlab.futunn.com/golang/ftrace"
	"gitlab.futunn.com/infra/frpc/pkg/application"
	trace "gitlab.futunn.com/infra/frpc/pkg/trace"
	"net/url"
	testing "testing"
)

func TestMain(m *testing.M) {
	application.Run(
		m.Run, // 等待测试任务完成后退出
		application.OnLoadConfig(
			application.LoadTestLogConfig(),  // 复用服务配置文件，但需要修改日志配置，路径为log/test.log.fls，级别为info
			application.DeleteServerConfig(), // 复用服务配置文件，但需要去掉服务端口配置，防止端口冲突导致启动失败
		),
		application.OnInit(
			application.InitTracerGoTest(1), // 为go test场景生成Tracer，并设置采样率
		),
	)
}

func printContext(ctx context.Context) {
	span := ftrace.SpanFromContext(ctx)
	fmt.Printf("trace地址: http://test.trace.server.com/trace/?id=%s \n", span.Context().TraceID)

	logUrl, _ := url.Parse("http://test.fls.server.com/fls/log?region=cn&limit=500&_type_%5B0%5D=framework&_type_%5B1%5D=access&_type_%5B2%5D=water")
	logUrlQuery := logUrl.Query()
	logUrlQuery.Set("_trace_id_", span.Context().TraceID.String())
	logUrlQuery.Set("service_name", appinfo.Get().App.Name)
	logUrl.RawQuery = logUrlQuery.Encode()
	fmt.Printf("日志地址: %s\n", logUrl.String())

	//fmt.Printf("Fmonitor视图地址：http://fmonitor.server.com/v3/view/tree/%s\n", appinfo.Get().App.Name)
}

func TestGetQuoteEndTimeByUid(t *testing.T) {
	span, ctx := trace.InitSpanContextForTest(context.Background())
	defer span.Finish()
	printContext(ctx)

	req := &pb.GetQuoteEndTimeByUidReq{}
	_, err := cli.GetQuoteEndTimeByUid(ctx, req)
	assert.NoError(t, err)
}

func TestBatchGetQuoteEndTime(t *testing.T) {
	span, ctx := trace.InitSpanContextForTest(context.Background())
	defer span.Finish()
	printContext(ctx)

	req := &pb.BatchGetQuoteEndTimeReq{}
	_, err := cli.BatchGetQuoteEndTime(ctx, req)
	assert.NoError(t, err)
}
