# 行情卡权限同步服务 (card_privilege_sync_service)

## 简介

`card_privilege_sync_service` 是一个用于同步行情卡权限信息到基础行情svr的服务。该服务依赖归属地服务来查询用户归属地，并根据用户归属地和客户端类型进行自转发路由。

## 功能

- **拉取用户权限**：提供uid查询和增量拉取两种方式获取用户行情卡权限。
- **自动路由**：基于用户的归属地信息和客户端类型，自动处理服务内部的路由转发逻辑。

## 依赖

- **归属地服务**：用于查询用户的归属地信息，并用于自动路由。[归属地服务](http://fservice.server.com/app/11425/overview)
- **数据库**：存储用户的行情卡权限信息。

## 架构

### 自动转发

自动路由逻辑实现于中间件中，根据用户的归属地信息和客户端类型进行服务内部的自转发。转发逻辑如下：

1. 查询用户归属地信息。
2. 根据srpc header携带的客户端类型和查询到的归属地信息，确定目标服务地区。
3. 自动将请求转发到对应地区的服务。

## 数据库

- **CN** 和 **HK**：部署了 NiuNiu 平台的数据。
- **US** 和 **JP**：部署了 Moomoo 平台的数据。
- **SG**：为了加速东南亚地区的访问，额外搭建了 US 的从库。

