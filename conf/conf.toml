[frpc]
name = "card_privilege_sync_service"


[frpc.server]
ip = "127.0.0.1"
pool_size = 100000
[frpc.server.srpc]
port = 9999
[frpc.server.http]
port = 19999

# 部署地区配置
[location_cfg]
location = "CN"

[frpc.mysql.qt_card_read]
username = "webfutu"
password = "futu1234@123"
address = "cmlb_38939"
db_name = "qt_card"

####### gorm插件配置 #######
[gorm_callback.sql_monitor]
conf_key = "qt_card_read"
enabled = true
monitor_specified_tables = ["user_pri_end_time"]
explain_sample_rate = 1
max_exec_time = "1ms"
max_scan_rows = 6000
panic_when_missed_index = true

####### 本地缓存 #######
[frpc.cache.statistic_info_cache]
size = "100MB"
default_expiry_time = "60s"

#### card_privilege_sync_service ####
[frpc.client.CardPrivilegeSync]
request_timeout = "2s"
address = "fns://card_privilege_sync_service"

#### user_attribution ####
[frpc.client.UsrAttributionSvr]
request_timeout = "1s"
address = "fns://user_attribution?g_service_area=cn"

[frpc.log]
level = "debug"