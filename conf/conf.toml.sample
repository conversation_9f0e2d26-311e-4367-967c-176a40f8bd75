[frpc]
#name = "frpc"                              # 【必填】微服务管理平台注册的应用名称，该字段关联FLS、FTRACE查询，请务必填写正确
#max_proc = 0                               # 【选填】默认与机器CPU核数一致，可显式指定
#time_location = ""                         # 【选填】全局时区，默认time.Local，即本机的时区，可配置"America/New_York"等时区


#[frpc.server]                              ##【可选模块】仅对外提供RPC/HTTP服务时需配置
#ip = "0.0.0.0"                             # 【必填】服务监听ip地址
#pool_size = 100000                         # 【选填】默认server协程池容量无限制，可根据业务过载保护设置具体值，一般不建议超过10万
#[frpc.server.srpc]
#port = 9999                                # 【必填】srpc端口号
#[frpc.server.http]
#port = 19999                               # 【必填】http端口号，该端口同时集成健康检查、metric、pprof等（建议在srpc端口号前加1，好记）


#[frpc.service]                             ##【可选模块】仅提供RPC/HTTP服务时需配置
#handle_timeout = "5s"                      # 【选填】默认服务端处理超时"5s"
#verify_ticket = false                      # 【选填】默认不开启验票
#flowcontrol_threshold = 0                  # 【选填】默认不开启流控
#flowcontrol_stat_interval = "1s"           # 【选填】默认1s，表示流控统计间隔


#[frpc.client]                              ##【可选模块】仅依赖外部RPC服务时需配置

#[frpc.client.foo]                          ##【可选模块】仅调用外部RPC服务时需配置，‘foo’是下游的 pbServiceName；
                                            ## 除了address，其他字段可在[frpc.client]中配置，表示对所有client都生效
#address = "cmlb_1234"                      # 【必填】下游服务地址。例如："test_{ip}:{port}"、"cmlb_{cmlbid}，详情参考：https://futu.feishu.cn/wiki/wikcnOSxAk4K66yhdWQfCbbGKHc#GmW4di6AaoUw04xo174cd5Y6nCg
#protocol = "srpc"                          # 【选填】默认协议srpc，可选择"http"
#persistent = true                          # 【选填】默认长连接，可选择短连接false
#cmlb_pkg_stats_enable = false              # 【选填】v1.12.3之后默认关闭cmlb回包统计，可选择开启true
#pool_size = 100000                         # 【选填】默认client协程池容量无限制，一般无需设置
#conn_size = 1                              # 【选填】默认单节点tcp连接池大小为1，仅针对srpc协议的长连接生效，如需提高调用吞吐量，推荐配置4、8等

#[frpc.client.foo.bar]                      ##【可选模块】仅调用外部RPC服务时需配置，‘foo’是下游的pbServiceName，‘bar’是下游的pbMethodName；
                                            ## 所有字段可在[frpc.client]中配置，表示对所有client都生效。也可在[frpc.client.foo]中配置，表示对下游服务是‘foo’的client生效
#request_timeout = "5s"                     # 【选填】默认为5s，可显式指定，例如“500ms”、“2s”等
#retry_max = 0                              # 【选填】默认为0，选择固定重试次数时需显式指定，重试逻辑仅当显式指定retry_max、retry_timeout两个字段中的至少一个时才生效
#retry_timeout = "0s"                       # 【选填】默认为0s，选择最大重试时间需显式指定
#retry_interval = "10ms"                    # 【选填】默认重试间隔10ms
#circuitbreaker_strategy = "None"           # 【可选模块】默认为None，表示不使用熔断。可选SlowRequestRatio/ErrorRatio/ErrorCount，具体配置可参考FRPC文档
#circuitbreaker_threshold = xxx             # 【必填】仅熔断策略不为None时配置，要求>=0，表示熔断触发的门限值，不同策略有不同含义
#circuitbreaker_stat_interval = xxx         # 【必填】仅熔断策略不为None时配置，要求>=1ms，表示熔断统计的时间窗口长度
#circuitbreaker_min_request_amount = xxx    # 【必填】仅熔断策略不为None时配置，要求>0，表示熔断触发的请求静默数量。当统计的请求数小于该值，不使用熔断
#circuitbreaker_retry_timeout = xxx         # 【必填】仅熔断策略不为None时配置，要求>=1ms，表示熔断触发后持续的时间
#circuitbreaker_max_allow_rt  = xxx         # 【必填】仅熔断策略不为None时配置，要求>=1ms，SlowRequestRatio策略下有效且必配，请求是否是慢调用的临界值


#[frpc.log]                                 ##【可选模块】以下是框架默认配置，如满足需求可不显式配置
#level = "info"                             # 【选填】默认info，可选项"error/warn/info/debug"
#file_path = "./log/service.log.fls"        # 【选填】默认服务日志文件路径，如需输出至控制台，配置为""即可
#max_backups = 10                           # 【选填】默认10，表示日志文件滚动数量
#max_size = "100MB"                         # 【选填】默认100MB，表示日志文件大小
#add_caller = true                          # 【选填】默认true，表示记录文件位置，修改为false可以减少日志写入成本
#async = true                               # 【选填】默认true，表示异步写入
#access_level = "debug"                     # 【选填】默认debug，可选info，表示服务访问日志等级，当该日志等级低于frpc.log.level时，不会输出访问日志
#sample_rate = 1                            # 【选填】默认1，表示全采样，可选项[1e-9, 1]
#sample_threshold = 100                     # 【选填】默认100，表示日志采样阈值，即每秒相同日志超过阈值时触发采样
#framework_level = "info"                   # 【选填】默认info，可选项"error/warn/info/debug"，表示框架日志的过滤级别，即比该级别更低的日志不输出

#[frpc.trace]                               ##【可选模块】以下是框架默认配置，如满足需求可不显式配置
#sample_rate = 0.001                        # 【选填】默认0.001，可以配置[0, 1]，若上游服务已采样，该配置不生效
#sample_threshold = 10                      # 【选填】默认10，可配置>0，在1s的统计周期中低于该值全采样，高于该值按采样率采样


#[frpc.metric]                              ##【可选模块】以下是框架默认配置，如满足需求可不显式配置
#enable_monitor = true                      # 【选填】默认true，表示启用monitor，后续接入prometheus可选择关闭monitor上报


#[frpc.tasksig]                             ##【可选模块】仅需要使用换票服务时配置
#biz_id = 9999                              # 【必填】需显式指定biz_id


#[frpc.cron.foo]                            ##【可选模块】仅需要使用cron定时任务时配置，‘foo’为cron实例名称，请指定自己的实例名称
#disable = false                            # 【选填】默认false，定时任务开关，如需关闭可指定true
#expr = "* * * * *"                         # 【选填】使用标准的cron表达式，注意expr和expr_with_sec必填其一
#expr_with_sec = "*/10 * * * * *"           # 【选填】使用扩展到秒级的cron表达式，注意expr和expr_with_sec必填其一
#time_location = "Local"                    # 【选填】默认time.Local，即本机的时区，可配置"America/New_York"等时区
#action_if_still_running = "skip"           # 【选填】默认skip，上个任务还未执行完成时如何处理下一个任务，可选择run/wait/skip，即立即执行/排队执行/跳过
#trace_sample_rate = 0.001                  # 【选填】默认0.001，表示cron任务的trace采样概率
#trace_service_name = ""                    # 【选填】trace_service_name的配置请参考FRPC Trace文档
#log_access_level = "info"                  # 【选填】默认info，可选debug，表示cron访问日志等级，当该日志等级小于frpc.log.level时，不会输出访问日志
#singleton_mode = false                     # 【选填】默认false，是否启用单实例模式（即部署多个cron实例只触发一个，基于redis分布式锁实现）
#singleton_expiry = "10s"                   # 【选填】默认10s，可配值>=1s，单实例模式分布式锁的有效时间



#[frpc.timer]                               ##【可选模块】仅需要使用timer定时任务时配置
#pool_size = 1000                           # 【选填】默认大小1000，表示timer库注册的定时任务的go程池大小

#[frpc.timer.foo]		                    ##【可选模块】仅需要特定的trace及log参数时配置，‘foo’为timer实例名称，请指定自己的实例名称
#disable = false							# 【选填】默认为false，使用此config_key注册的定时器的开关，true时该定时器将不会运行
#trace_sample_rate = 0.001                  # 【选填】默认0.001，trace采样率设置
#trace_sample_threshold = 10                # 【选填】默认10，trace采样阈值
#trace_service_name = ""                    # 【选填】trace_service_name的配置请参考FRPC Trace文档
#log_access_level = "debug"                 # 【选填】默认debug，可选info，表示timer访问日志等级，当该日志等级小于frpc.log.level时，不会输出访问日志


#[frpc.mysql.default]                       ##【可选模块】mysql全局配置
#collation = "utf8mb4_general_ci"           # 【选填】默认为utf8mb4_general_ci，表示连接使用的编码字符集
#dial_timeout = "10s"                       # 【选填】默认为系统默认值，表示连接超时，默认10s
#read_timeout = "30s"                       # 【选填】默认30s，表示I/O读超时
#write_timeout = "60s"                      # 【选填】默认60s，表示I/O写超时
#max_idle_conns = 100                       # 【选填】默认100，表示最大空闲连接数，0表示不保留空闲连接，若服务TPS较高时可适当调大该值
#max_open_conns = 200                       # 【选填】默认200，表示连接池的大小，即空闲连接不够时可建立的连接上限，若该值不为0，则必须大于max_idle_conns
#conn_max_life_time = "300s"                # 【选填】默认300s，表示连接的最大生命周期，通常将该时间设置较大，但小于mysqld端wait_timeout/2
#interpolate_params = true                  # 【选填】默认true，表示不使用prepare预查询，可选false


#[frpc.mysql.foo]                           ##【可选模块】仅需要使用mysql组件时配置，‘foo’为数据库实例名称，请指定自己的实例名称
#username = "username"                      # 【必填】用户名
#password = "password"                      # 【必填】密码
#address = "cmlb_1234"                      # 【必填】地址。例如："test_{ip}:{port}"、"cmlb_{cmlbid}，详情参考：https://futu.feishu.cn/wiki/wikcnOSxAk4K66yhdWQfCbbGKHc#GmW4di6AaoUw04xo174cd5Y6nCg
#db_name = "frpc_test"                      # 【必填】DB名


#[frpc.redis.default]                       ##【可选模块】Redis全局配置
#max_retries = 0                            # 【选填】默认为0，即不重试，表示命令执行失败后的最大重试次数
#min_retry_backoff = "8ms"                  # 【选填】默认为8ms，表示每次重试的最小退避间隔
#max_retry_backoff = "512ms"                # 【选填】默认为512ms，表示每次重试的最大退避间隔
#dial_timeout = "5s"                        # 【选填】默认为5s，表示连接超时
#read_timeout = "3s"                        # 【选填】默认为3s，表示socket读超时
#write_timeout = "3s"                       # 【选填】默认与read_timeout一致，表示socket写超时
#pool_size = 20                             # 【选填】默认为10 * CPU核数（runtime.NumCPU），表示连接池大小
#min_idle_conns = 0                         # 【选填】默认为0，表示最小空闲连接数
#max_conn_age = "1h"                        # 【选填】默认为0，即连接不会过期，表示最大连接生命周期，超过期限的空闲连接会被关闭
#pool_timeout = "4s"                        # 【选填】默认为read_timeout + 1s，表示无空闲连接时最长等待时间
#idle_timeout = "5m"                        # 【选填】默认为5分钟，表示空闲连接时间
#idle_check_frequency = "1m"                # 【选填】默认为1分钟，表示空闲连接检查频率（reaper执行间隔）


#[frpc.redis.foo]                           ##【可选模块】仅需要使用redis组件时配置，‘foo’为redis实例名称，请指定自己的实例名称
#address = "cmlb_1234"                      # 【必填】地址。例如："test_{ip}:{port}"、"cmlb_{cmlbid}，详情参考：https://futu.feishu.cn/wiki/wikcnOSxAk4K66yhdWQfCbbGKHc#GmW4di6AaoUw04xo174cd5Y6nCg
#password = "password"                      # 【必填】密码
#db = 0                                     # 【选填】db，默认为0


#[frpc.rmq.foo]                             ##【可选模块】仅需要使用RMQ组件时配置，‘foo’为RMQ实例名称，请指定自己的实例名称
#address = "cmlb_1234"                      # 【必填】服务地址。例如："test_{ip}:{port}"、"cmlb_{cmlbid}，详情参考：https://futu.feishu.cn/wiki/wikcnOSxAk4K66yhdWQfCbbGKHc#GmW4di6AaoUw04xo174cd5Y6nCg
#username = "username"                      # 【必填】用户名
#password = "password"                      # 【必填】密码
#vhost = "/"                                # 【选填】默认为/，virtual host
#scheme = "amqp"                            # 【选填】默认为amqp，scheme
#dial_timeout = "1s"                        # 【选填】默认为1s，表示连接超时时间

#[[frpc.rmq.foo.queues]]                    ##【可选模块】服务启动后会自动根据配置声明queue，请确保账户具有配置权限
#queue = "all"                              # 【必填】队列名
#durable = false                            # 【选填】默认为false，表示队列是否持久


#[frpc.rmq.foo.consumers.bar]               ##【可选模块】仅需要使用RMQ Consumer(Worker)时配置，‘foo’为RMQ实例名称，‘bar’为worker实例名称，请指定自己的实例名称
#worker_number = 4                          # 【必填】启动的worker goroutines数量
#queue = "all"                              # 【必填】消费的消息队列名
#prefetch_count = 16                        # 【选填】默认为0，即不启用，如果配置则会在创建Channel后自动设置Qos配置
#consumer = ""                              # 【选填】默认为空，即自动生成，表示Channel上唯一的Consumer标识符
#auto_ack = false                           # 【选填】默认为false，即需要手动执行Ack方法
#exclusive = false                          # 【选填】默认为false，表示是否独占
#no_wait = false                            # 【选填】
                                            ## 如果Consumer需要采样，以下字段需配置
#trace_sample_rate = 0.001                  # 【选填】默认0.001，表示Trace采样率设置
#trace_sample_threshold = 10                # 【选填】默认10，trace采样阈值
#trace_service_name = ""                    # 【选填】trace_service_name的配置请参考FRPC Trace文档


#[frpc.kafka.foo]                           ##【可选模块】仅需要使用kafka组件时配置，‘foo’为kafka实例名称，请指定自己的实例名称
#address = "cmlb_1234"                      # 【必填】服务地址。例如："test_{ip}:{port}"、"cmlb_{cmlbid}，详情参考：https://futu.feishu.cn/wiki/wikcnOSxAk4K66yhdWQfCbbGKHc#GmW4di6AaoUw04xo174cd5Y6nCg
#client_id = "sarama"                       # 【选填】client名，kafka日志&监控使用
#version = "1.0.0"                          # 【选填】Kafka实例的版本号，默认配置为v1.0.0，注意不可低于实际实例版本号，否则可能会启动失败

#[frpc.kafka.foo.net]                       ##【可选模块】kafka组件 - 网络配置
#max_open_requests = 5                      # 【选填】最大连接数，注意该实例下的所有SyncProducer共享此连接数，所有AsyncProducer共享此连接数，每个Consumer单独计算连接数
#dial_timeout = "30s"                       # 【选填】连接超时时间
#read_timeout = "30s"                       # 【选填】读超时时间
#write_timeout = "30s"                      # 【选填】写超时时间

#[frpc.kafka.foo.producer]                  ##【可选模块】kafka组件 - Producer配置
#return.successes = true                    # 【选填】发送消息成功后是否发送通知到Success Channel(仅对AsyncProducer有效，如果启用必须处理此Channel的消息)
#return.errors = true                       # 【选填】发送消息失败后是否发送通知到Error  Channel(仅对AsyncProducer有效，如果启用必须处理此Channel的消息)
#required_acks = "WaitForLocal"             # 【选填】ack级别，可选值：NoResponse/WaitForLocal/WaitForAll（NoResponse指收到socket ack）

#[frpc.kafka.foo.producer.flush]            ##【可选模块】kafka组件 - Producer配置 - Flush配置
#bytes = 0                                  # 【选填】触发flush（发送到broker）的字节数
#messages = 0                               # 【选填】触发flush的消息数
#frequency = "0"                            # 【选填】触发flush的间隔时间

#[frpc.kafka.foo.consumers.bar]             ##【可选模块】仅需要使用kafka Consumer(Worker)时配置，‘foo’为kafka实例名称，‘bar’为worker实例名称，请指定自己的实例名称
#topics = ["example"]                       # 【选填】订阅的topic列表
#max_wait_time = "250ms"                    # 【选填】
#max_processing_time = "100ms"              # 【选填】
#isolation_level = "ReadUncommitted"        # 【选填】
#trace_sample_rate = 0.001                  # 【选填】Trace采样率设置，默认0.001
#trace_sample_threshold = 10                # 【选填】trace采样阈值，默认10
#trace_service_name = ""                    # 【选填】trace_service_name的配置请参考FRPC Trace文档

#[frpc.kafka.foo.consumers.bar.group]       ##【可选模块】kafka组件 - Consumer配置 - Group配置
#id = ""                                    # 【必填】group id

#[frpc.kafka.foo.consumers.bar.offsets]     ##【可选模块】kafka组件 - Consumer配置 - Offset配置
#auto_commit.enable = true                  # 【选填】是否试用自动提交
#auto_commit.interval = "1s"                # 【选填】自动提交间隔时间
#initial = -1                               # 【选填】消费起始位置 -1:Newest（最新的偏移量）, -2: Oldest（上一次提交的偏移量）
#retention = 0                              # 【选填】偏移量保留时间，默认禁用（0）

#[frpc.kafka.foo.consumers.bar.fetch]       ##【可选模块】kafka组件 - Consumer配置 - Fetch配置
#min = 1                                    # 【选填】最小消息字节数
#default = 1048576                          # 【选填】broker发送的默认消息大小
#max = 0                                    # 【选填】单次最大消息字节数，默认无限制(0)


#[frpc.http.clients.default]                ##【可选模块】HTTP Client全局基础配置


#[frpc.http.clients.foo]                    ##【可选模块】需要特定配置的HTTP Client时配置，无配置使用默认Client
#scheme = "http"                            # 【选填】自动拼接仅含path部分的url时使用，选填"http"/"https"
#address = ""                               # 【选填】自动拼接仅含path部分的url时使用
#disable_keep_alive = false                 # 【选填】HTTP “Keep-Alive”选项，默认false
#disable_compression = false                # 【选填】未显式设置“Accept-Encoding”时，客户端自动使用gzip对请求/响应的Body进行压缩/解压缩，默认false
#max_idle_conn = 100                        # 【选填】最大空闲连接数，0无限制，默认为100
#max_idle_per_host = 2                      # 【选填】每个Host最大空闲连接数，默认为2，0无限制
#max_conn_per_host = 0                      # 【选填】每个Host最大建立中、活跃和空闲连接总数，默认为0无限制
#request_timeout = "0"                      # 【选填】请求超时，包括连接、重定向及读取响应Body的时间。注意：超时会中断Get/Post/Do/Head接口返回的Response.Body的读取。默认为0无限制
#idle_conn_timeout = "0"                    # 【选填】空闲连接超时时间，默认为0无限制
#response_header_timeout = "0"              # 【选填】完全发送请求后，接收响应HEADER的超时时间，默认为0无限制
#expect_continue_timeout = "0"              # 【选填】完全发送"Expect: 100-continue"请求HEADER后，等待响应HEADER的超时时间，0表示不等待响应直接发送Body
#write_buffer_size = 0                      # 【选填】写缓冲大小，0使用默认值4KB
#read_buffer_size = 0                       # 【选填】读缓冲大小，0使用默认值4KB
#max_response_header_bytes = 0              # 【选填】响应HEADER大小，0无限制
#transport_timeout = "30s"                  # 【选填】TCP建立连接超时时间，具体超时取决于平台实现
#transport_keep_alive = "30s"               # 【选填】TCP心跳，具体时间取决于平台实现，0默认15s，负数禁用


##[frpc.http.clients.default.tls]           ##【可选模块】HTTP Client TLS全局配置


#[frpc.http.clients.foo.tls]                ##【可选模块】仅需要TLS时进行配置
#handshake_timeout = "30s"                  # 【选填】默认30s，TLS握手超时时间，0无限制
#server_name = ""                           # 【选填】显示指示client请求的server host name
#rootca_certs = []                          # 【选填】Client验证server证书时使用的根证书，此选项如有需要请直接填写证书内容
#rootca_certs_path = []                     # 【选填】Client验证server证书时使用的根证书，此选项为证书文件路径，不配置rootca_certs/rootca_certs_path默认使用主机下的根证书集
#skip_verify = false                        # 【选填】true不对server证书链和host name进行校验
#disable_dynamic_record_size = true         # 【选填】true禁止动态调整TLS Record大小，并且一直使用可用的最大大小
#client_certs = []                          # 【选填】Client证书内容，Client需要认证时设置
#client_private_keys = []                   # 【选填】Client私钥内容，请与client_certs中的配置一一对应
#client_certs_path = []                     # 【选填】Client证书路径，Client需要认证时设置
#client_private_keys_path = []              # 【选填】Client密钥路径，请与client_certs_path中的配置一一对应


#[frpc.http.clients.default.proxy]          ##【可选模块】HTTP Client Proxy全局配置


#[frpc.http.clients.foo.proxy]              ##【可选模块】仅需要设置静态Proxy时配置
#http_proxy = ""                            # 【选填】http请求使用的代理地址，支持直接填写cmlb_id、http://cmlb_id/
#https_proxy = ""                           # 【选填】https请求使用的代理地址，支持直接填写cmlb_id（但等效于http://cmlb_id而非https://cmlb_id)
#from_environment = false                   # 【选填】从环境变量获取代理地址，不存在则不使用代理，默认false
#scheme = ""                                # 【选填】认证类型
#user = ""                                  # 【选填】认证类型为Basic时使用
#password = ""                              # 【选填】认证类型为Basic时使用
#token = ""                                 # 【选填】认证类型非Basic时使用


#[frpc.i18n]                                ##【可选模块】仅需使用多语言时配置
#domains = ["default"]                      # 【选填】domains列表，第一个domain为默认domain名
#default_locale_code = "en_US"              # 【选填】默认语言代码
#path = "./locale"                          # 【选填】语言资源文件路径
