; 此配置仅用于开发环境调试用，测试和线上环境需要使用Fservice发布后下发的应用信息
; 应用信息文件说明：https://futu.feishu.cn/wiki/wikcnVe61YnEXKdVA6fISuJJlDe

; 应用信息
[app]
;; name 指Fservice上的应用名，必填
name=card_privilege_sync_service

;; 部署组端口配置，选填，使用服务自注册时才需要填写
;[app.deploy_group.port]
;3006=SRPC
;5200=HTTP

;; 实例信息
[app.deploy_group.instance]
;; ip_address 实例部署的机器IP，选填，使用服务自注册时才需要填写，如留空则自动取CMDB_HOST_IP环境变量值
ip_address=
;; install_path 部署路径，选填，使用服务自注册时才需要填写
install_path=

;; 命名服务配置，使用FNS寻址时需要填写
[naming_service]
;; token FNS Agent的Consul Token，使用FNS则必填
token=68a119b3-1b2a-f8b9-0426-3f75da72a96a

;; weight 节点权重，使用服务自注册时才需要填写，取值范围1~65535
;weight=100
;; self_register 是否启用服务自注册功能，一般不需要启用
self_register=false

; 命名服务标签
[naming_service.tags]
; 基础标签必须都要填，介绍文档：https://futu.feishu.cn/wiki/wikcnBCRH7cFZT4NVSN0ju79tgd
; env 环境，必填，只能配置为开发环境，线上环境和测试环境需要通过Fservice下发
env = dev
; entity 主体，必填，候选值：hk_sec/us_sec/sg_sec/au_sec/jp_sec/futunn/hk_nn/moomoo/us_clearing/us_crypto/...
entity = futunn
; role 网络角色，必填，只能配置为开发网，线上环境和测试环境需要通过Fservice下发
role = dev_net
; region 地域，必填，候选值：guangzhou/shanghai/hongkong/singapore/california/virginia/sydney/...
region = guangzhou
; area 地区，必填，候选值：cn/us/sg/au/jp/...
area = cn
; zone 可用区，选填
; zone =

; 自定义标签，需根据当前应用的路由标签配置填写
;g_public_tag=
;your_app_name.private_tag=
