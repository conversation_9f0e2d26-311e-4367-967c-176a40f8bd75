attrs:
  is_background_program: 0
foreground_program:
- program_name: card_privilege_sync_service
  supervisor_conf: |
    program_name=card_privilege_sync_service
    command=#INSTALL_PATH/bin/card_privilege_sync_service -config #INSTALL_PATH/conf/conf.toml
    stopsignal=TERM
    directory=#INSTALL_PATH
    numprocs=1
    process_name=%(program_name)s
    startsecs=3
    startretries=3
    autorestart=true
    exitcodes=0,2
    stopwaitsecs=45
    environment=LANG="en_US.UTF-8",LC_ALL="en_US.UTF-8"
    stopasgroup=true
    killasgroup=true
    user = ops
    ;umask = 022
    ;priority = 999
    autostart = false
    ;redirect_stderr = false
    stdout_logfile = /data/log/#PKG_NAME/%(program_name)s_stdout.log
    stdout_logfile_maxbytes = 100MB
    stdout_logfile_backups = 10
    ;stdout_capture_maxbytes = 1MB
    stdout_events_enabled = false
    stderr_logfile = /data/log/#PKG_NAME/%(program_name)s_stderr.log
    stderr_logfile_maxbytes = 100MB
    stderr_logfile_backups = 10
    stderr_capture_maxbytes = 1MB
    ;stderr_events_enabled = false
    ;serverurl = AUTO
