PROJECT_NAME := "card_privilege_sync_service"
PKG := "gitlab.futunn.com/web_data_application/card_privilege_sync_service"
PKG_LIST := $(shell go mod tidy && go list ${PKG}/...)
BUS_PKG_LIST := $(shell go mod tidy && go list ${PKG}/... | grep -v stub_test)
GO_FILES := $(shell find . -name '*.go' | grep -v _test.go)
COVERAGE_PKG_LIST := $(shell go mod tidy && go list ${PKG}/... | grep -E -v "cmd|stub_test|pb")
COVERAGE_PKGS := $(shell echo ${COVERAGE_PKG_LIST} | sed  "s/ /,/g")
UNITTEST_COV = "unittest_cov"

##set default go env proxy
#export GOPROXY="https://mirrors.tencent.com/go/,direct"
export GONOPROXY="*.futunn.com,*.oa.com"
export GONOSUMDB="*.futunn.com,*.oa.com"
export GOPRIVATE="*.futunn.com,*.oa.com"
export GOINSECURE="*.futunn.com,*.oa.com"

##set trace/monitor/cmlb proxy for windows and mac
#export HOST_NETWORK_ENVIRONMENT=DEV
#export CMLB_PROXY_URL=http://************:7777
#export MONITOR_PROXY_URL=http://************:8899
#export FTRACE_UDP_AGENT_HOST=************:5831
#export CONSUL_AGENT_ADDRESS=************:8500

.DEFAULT_GOAL := default
.PHONY: all

all: fmt lint vet test race build

dep: ## Get dependencies
	@echo "go dep..."
	@go mod tidy

fmt: dep ## Format code
	@echo "go fmt..."
	@go fmt $(PKG_LIST)

lint: dep ## Lint check
	@echo "golangci-lint run ..."
	@if command -v golangci-lint >/dev/null 2>&1;\
	then echo 'exist golangci-lint';\
	else (export URL=http://************/static;curl -fsSL $$URL/install_lint.sh | sh);\
	fi
	@golangci-lint run

vet: dep ## Vet check
	@echo "go vet..."
	@go vet -all $(PKG_LIST)

test: dep ## Run unittests
	@echo "go test..."
	@go test -gcflags=all=-l -short -v -count=1 -coverprofile=${UNITTEST_COV}/cover.out -coverpkg=${COVERAGE_PKGS} ${BUS_PKG_LIST}
	@go tool cover -html=${UNITTEST_COV}/cover.out -o ${UNITTEST_COV}/cover.html

race: dep ## Run data race detector
	@echo "go test race..."
	@go test -gcflags=all=-l -race -short -v -count=1 ${BUS_PKG_LIST}

#msan: dep ## Run memory sanitizer
	@#go test -gcflags=all=-l -msan -short ${PKG_LIST}

build: dep fmt ## Build frpc project
	@echo "go build..."
	@CGO_ENABLED=1 go build -v -buildmode=default -o bin/${PROJECT_NAME} cmd/main.go
#	@CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -v -gcflags=all="-N -l" -o bin/${PROJECT_NAME} cmd/main.go
	@chmod +x bin/${PROJECT_NAME}

build-dev: dep fmt ## Build frpc project
	@echo "go build..."
	@go build -v -gcflags=all="-N -l" -o bin/${PROJECT_NAME} cmd/main.go
	@chmod +x bin/${PROJECT_NAME}

run: dep fmt ## Run frpc project
	@echo "go run..."
	@go run cmd/main.go --config=conf/conf.toml

start: build ## Start frpc project
	@echo "start service in background mode..."
	@nohup bin/${PROJECT_NAME} --config=conf/conf.toml >/dev/null 2>&1 &

stop: ## Stop frpc project
	@echo "stop service..."
	@ps ux | grep ${PROJECT_NAME} | grep -v grep | awk '{print $$2}' | xargs kill -9

#restart: stop start ## Restart frpc project

stub_test: fmt ## Run stub test
	@echo "go run stub_test..."
	@go test -gcflags=all=-l -v -count=1 ./stub_test/...

help: ## Display this help screen
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

default: help

