syntax = "proto2";

import "srpc.proto";

package card_privilege_sync_service;
option py_generic_services = true;

// 错误码通过srpc头部err_info字段返回
enum ErrorCode
{
  SUCCEED = 0; // 成功
  INTERNAL_SERVER_ERR = 1;//服务内部错误
  PARAMETER_INVALID = 2;//参数错误
  UNKNOWN = 99; //未知错误
}

// 行情卡权限类型，命名规则：市场 + 权限类型 + 权限等级（可选）
enum QuoteType
{
  HK_STOCK_LV2 = 1;             // 港股lv2
  HK_DERIVATIVES_LV2 = 2;       // 港股期权期货lv2
  HK_STOCK_FULL_TICK = 3;       // 港股高级全盘行情
  US_TOTAL_VIEW = 4;            // 美股totalview
  US_OPEN_BOOK = 5;             // 美股openBook
  US_NATIONAL_LV1 = 6;          // 美股全美综合报价
  US_OPTION_LV2 = 7;            // 美股期权深度摆盘
  US_OPTION_REAL_TIME = 8;      // 美股期权实时行情
  OPTION_EXERCISE_PROBABILITY = 9; // 期权行权概率
  US_NASDAQ_BASIC_API = 10;     // 美股纳斯达克基础行情（API）
  CN_STOCK_LV2_DESKTOP = 11 [deprecated=true]; // A股lv2桌面版，该行情已下线
  CN_STOCK_LV2_MOBILE = 12 [deprecated=true]; // A股lv2移动版，该行情已下线
  US_TOTAL_VIEW_API = 13;       // 美股totalview（API）
  US_OPTION_REAL_TIME_API = 14; // 美股期权实时行情（API）
  US_OPTION_ORDER_BOOK_API = 15;// 美股期权深度摆盘（API）
  US_OPTION_DEPTH_QUOTES_API = 16;// 美股期权深度行情（API）
  SG_FUTURES_LV2 = 17;          // 新加坡期货lv2
  SG_FUTURES_LV1 = 18;          // 新加坡期货lv1
  SG_STOCK_LV2 = 19;            // 新加坡股票lv2
  SG_STOCK_LV1 = 20;            // 新加坡股票lv1
  GLOBAL_INDEX_REAL_TIME = 21; // 全球指数实时行情
  JP_FUTURES_LV2 = 22;          // 日本期货lv2
  FINANCIAL_ANALYSIS = 23;      // 财务解读
  US_CBOE_BZX = 24;             // 美股CBOE bzx lv2
  US_CBOE_EDGE = 25;            // 美股CBOE direct edge lv2
  INTELLIGENT_PATTERN_INTERPRETATION = 26; // 智能形态解读
  CA_LV1_REAL_TIME = 27; // 加拿大LV1实时行情（旧）
  HK_STOCK_LV1_FOR_MOBILE = 28; // 港股LV1实时行情(移动端)(已下架)
  AU_STOCK_LV2 = 29;            // 澳洲股票lv2
  US_CME_FUTURE_LV2 = 30;       // 美股CME期货lv2
  US_CBOT_FUTURE_LV2 = 31;      // 美股CBOT期货lv2
  US_NYMEX_FUTURE_LV2 = 32;     // 美股NYMEX期货lv2
  US_COMEX_FUTURE_LV2 = 33;     // 美股COMEX期货lv2
  US_NYSE_ARCA_BOOK = 34;       // 美股NYSE ArcaBook
  JP_FUTURES_LV1_API = 35;      // 日本期货LV1行情（API）
  SG_FUTURES_LV1_API = 36;      // 新加坡期货LV1行情（API）
  US_OTC_STOCK_LV1_API = 37;    // 美股OTC股票实时行情（API）
  US_INDEX_LV1_API = 38;        // 美股指数实时行情（API）
  CN_STOCK_LV1_SH_API = 39;   // A股上证LV1行情（API）
  CN_STOCK_LV1_SZ_API = 40;   // A股深证LV1行情（API）
  MY_STOCK_LV2 = 41;            // 马来西亚股票lv2
  MY_STOCK_LV3 = 42;            // 马来西亚股票lv3
  MY_FUTURES_LV2 = 43;          // 马来西亚期货lv2
  US_ARCA_BOOK_LIMITED_TIME = 44 [deprecated=true]; // arcabook限时激活类型，已废弃，该权限由增长的premium服务替代
  CA_TMX_LV1_OLD = 45 [deprecated=true]; // 加拿大TMX Lv1，已废弃，用CA_TMX_LV1替代
  CA_TSX_DEPTH_BOOK = 46;       // 加拿大TSX深度摆盘
  CA_TSXV_DEPTH_BOOK = 47;      // 加拿大TSXV深度摆盘
  CA_CSE_LV1 = 48 [deprecated=true]; // 加拿大CSE，已废弃
  CA_CSE_DEPTH_BOOK = 49;       // 加拿大CSE
  MY_STOCK_LV1 = 50;            // 马来西亚股票lv1
  CA_TORONTO_LV1_MAIN = 51;     // 加拿大多伦多LV1实时行情
  CA_TORONTO_LV1_VENTURE = 52;  // 加拿大多伦多Venture LV1实时行情
  JP_TSE_LV1 = 53;              // 日本TSE LV1 1档摆盘
  JP_TSE_LV2 = 54;              // 日本TSE LV2 10档摆盘
  JP_TSE_LV3 = 55;              // 日本TSE LV3 60档摆盘
  PREMIUM = 56;                 // premium权限，目前是增长侧premium服务对接
  JP_OPTIONS_LV2 = 57;          // 日本期权lv2
  MY_FUTURES_LV1 = 58;          // 马来西亚期货lv1
}

// 根据uid拉取行情卡权限到期时间
// uid通过header的origin_uid传递
// header头部的user_attribution必传，用于区分用户归属地路由
// header头部的client_type必传，用于区分牛牛和moomoo路由
// client_version建议也填一下
message GetQuoteEndTimeByUidReq
{
  optional bool include_expire_info = 1; //是否需要过期权限信息，true-需要 false-不需要。默认false
  repeated int32 quote_type_list = 2; // 筛选权限类型，参考QuoteType枚举，不传则拉取所有权限
}

message GetQuoteEndTimeByUidRsp
{
  repeated QuoteEndTimeInfo list = 1; //返回数据
  optional PlatformInfo platform_info = 2; //平台信息，用户问题排查

  message QuoteEndTimeInfo
  {
    optional int32 quote_type = 1; //参考QuoteType枚举
    optional int64 expire_timestamp = 2; //权限到期时间，秒级时间戳
  }
}

// 增量拉取用户行情卡权限到期时间接口
// header头部的user_attribution必传，用于区分用户归属地路由
// header头部的client_type必传，用于区分牛牛和moomoo路由
message BatchGetQuoteEndTimeReq
{
  optional string page_token = 1; // 游标，初次拉取不传或传空。后续拉取传上次返回的page_token。如果传的page_token无效，视作初次拉取
  optional int32 page_size = 2; // 每次拉取的最大数量，最多5000。
  repeated int32 quote_type_list = 3; // 权限类型，参考QuoteType枚举，不传则拉取所有权限
}

message BatchGetQuoteEndTimeRsp
{
  repeated QuoteEndTimeInfoGroup quote_end_time_info_groups = 1; //返回数据
  optional string next_page_token = 2; // 游标，用于下次拉取
  optional PlatformInfo platform_info = 3; //平台信息，用户问题排查
}

message QuoteEndTimeInfoGroup
{
  optional int32 quote_type = 1; // 权限类型，参考QuoteType枚举
  repeated Item items = 2;

  message Item {
    optional uint64 uid = 1;
    optional int64 expire_timestamp = 2; //权限到期时间，秒级时间戳
  }
}

message PlatformInfo
{
  optional string platform = 1; // 平台，牛牛 or moomoo
  optional string location = 2; // 地区
}

service CardPrivilegeSync {
  option (srpc.service_option_id) = 0xc125;

  // 根据uid查询行情卡到期时间
  rpc GetQuoteEndTimeByUid (GetQuoteEndTimeByUidReq) returns (GetQuoteEndTimeByUidRsp) {
    option (srpc.method_option_id) = 0x1;
    option (srpc.method_option_http_api) = {
      post: "/card-privilege-sync/get-quote-end-time-by-uid"
    };
    option (srpc.method_option_retry) = true;
  }

  // 增量查询行情卡到期时间
  rpc BatchGetQuoteEndTime (BatchGetQuoteEndTimeReq) returns (BatchGetQuoteEndTimeRsp) {
    option (srpc.method_option_id) = 0x2;
    option (srpc.method_option_http_api) = {
      post: "/card-privilege-sync/batch-get-quote-end-time"
    };
    option (srpc.method_option_retry) = true;
  }
}