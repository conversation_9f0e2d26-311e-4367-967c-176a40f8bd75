# 作者：marson
# 更新日期：2023-12-18
# 版本要求：golangci-lint v1.55.2
# 说明：可以通过 https://futu.feishu.cn/docx/R6bZdLthQo8crYxzWKWcXesanjd 找到最新版本的配置
# 交流群：https://applink.feishu.cn/client/chat/chatter/add_by_link?link_token=70ai3df7-f6f9-4ee2-8ec1-8d6b896b09ba

linters-settings:
  tagliatelle:
    # Check the struck tag name case.
    case:
      # Use the struct field name to check the name of the struct tag.
      # Default: false
      use-field-name: true
      rules:
        # Any struct tag type can be used.
        # Support string case: `camel`, `pascal`, `kebab`, `snake`, `upperSnake`, `goCamel`, `goPascal`, `goKebab`, `goSnake`, `upper`, `lower`, `header`.
        json: snake
        yaml: snake
        xml: camel
        toml: snake
  typecheck: null
  cyclop:
    max-complexity: 15
    package-average: 10.0
    ignore-generated-header: true
  errcheck:
    check-type-assertions: true
    check-blank: true
  exhaustive:
    check:
      - switch
      - map
  ginkgolinter:
    suppress-len-assertion: true
    suppress-nil-assertion: true
    suppress-err-assertion: true
    suppress-compare-assertion: true
    suppress-async-assertion: true
    allow-havelen-zero: true
  exhaustruct:
    exclude:
      - ^net/http.Client$
      - ^net/http.Cookie$
      - ^net/http.Request$
      - ^net/http.Response$
      - ^net/http.Server$
      - ^net/http.Transport$
      - ^net/url.URL$
      - ^os/exec.Cmd$
      - ^reflect.StructField$
      - ^github.com/Shopify/sarama.Config$
      - ^github.com/Shopify/sarama.ProducerMessage$
      - ^github.com/mitchellh/mapstructure.DecoderConfig$
      - ^github.com/prometheus/client_golang/.+Opts$
      - ^github.com/spf13/cobra.Command$
      - ^github.com/spf13/cobra.CompletionOptions$
      - ^github.com/stretchr/testify/mock.Mock$
      - ^github.com/testcontainers/testcontainers-go.+Request$
      - ^github.com/testcontainers/testcontainers-go.FromDockerfile$
      - ^golang.org/x/tools/go/analysis.Analyzer$
      - ^google.golang.org/protobuf/.+Options$
      - ^gopkg.in/yaml.v3.Node$
  funlen:
    lines: 160
    statements: 120
  wrapcheck:
    ignoreSigs:
      - Errorf(
      - errors.New(
      - errors.Unwrap(
      - .Wrap(
      - .Wrapf(
      - .WithMessage(
      - .WithMessagef(
      - .WithStack(
      - .Transaction(f
    ignorePackageGlobs:
      - encoding/*
      - github.com/pkg/*
      - gitlab.futunn.com/infra/frpc/pkg/*
      - gitlab.futunn.com/golang/uft
  gocognit:
    min-complexity: 10
  gocritic:
    settings:
      captLocal:
        paramsOnly: false
      underef:
        skipRecvDeref: false
  gomnd:
    ignored-functions:
      - os.Chmod
      - os.Mkdir
      - os.MkdirAll
      - os.OpenFile
      - os.WriteFile
      - os.Exit
      - prometheus.ExponentialBuckets
      - prometheus.ExponentialBucketsRange
      - prometheus.LinearBuckets
      - NewError
      - StartWorkflowOptions
      - decimal.NewFromInt
      - decimal.NewFromFloat
      - decimal.Round
      - decimal.RoundDown
      - Decimal.RoundDown
      - decimal.Decimal.RoundDown
      - RoundDown
      - checkPrecision
      - proto.Uint32
      - foss.ExpireDay
      - temporal.RetryPolicy
      - time.Duration
      - workflow.ActivityOptions
    ignored-files:
      - .*\.pb\.go$
      - .*\.validate\.go$
      - .*\.gen\.go$
      - activities\.go$
      - workflow\.go$
    ignored-struct: null
    ignored-numbers:
      - '0666'
      - '0644'
      - '0755'
      - '10000'
      - '100'
      - '0'
      - '3'
      - '5'
      - '10'
      - '2.0'
  gomodguard:
    blocked:
      modules:
        - github.com/golang/protobuf:
            recommendations:
              - google.golang.org/protobuf
            reason: see https://developers.google.com/protocol-buffers/docs/reference/go/faq#modules
        - github.com/satori/go.uuid:
            recommendations:
              - github.com/google/uuid
            reason: satori's package is not maintained
        - github.com/gofrs/uuid:
            recommendations:
              - github.com/google/uuid
            reason: gofrs' package is not go module
        - gitlab.futunn.com/golang/metric:
            recommendations:
              - gitlab.futunn.com/golang/fmonitor
            reason: see https://futu.feishu.cn/docx/QjGrdFqMZo3D82xrrVOcvWbhnbh#Rnx4djPTCoItRRxvka5c42fHnYe
        - gitlab.futunn.com/infra/frpc/pkg/metric:
            recommendations:
              - gitlab.futunn.com/golang/fmonitor
            reason: see https://futu.feishu.cn/docx/QjGrdFqMZo3D82xrrVOcvWbhnbh#Rnx4djPTCoItRRxvka5c42fHnYe
  govet:
    enable-all: true
    disable:
      - fieldalignment
    settings:
      shadow:
        strict: true
      printf:
        funcs:
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf
  nakedret:
    max-func-lines: 0
  nolintlint:
    allow-no-explanation:
      - funlen
      - gocognit
      - lll
    require-explanation: true
    require-specific: true
    allow-leading-space: true
    allow-unused: false
  dupl:
    threshold: 200
  gci:
    local-prefixes: github.com/golangci/golangci-lint
  goconst:
    min-len: 2
    min-occurrences: 3
    ignore-tests: true
    numbers: true
  lll:
    line-length: 120
  rowserrcheck:
    packages:
      - github.com/jmoiron/sqlx
  tenv:
    all: true
  revive:
    ignore-generated-header: true
    severity: warning
    rules:
      - name: argument-limit
        arguments:
          - 6
      - name: atomic
      - name: confusing-naming
      - name: confusing-results
      - name: constant-logical-expr
      - name: context-as-argument
      - name: datarace
      - name: deep-exit
      - name: defer
      - name: early-return
      - name: empty-block
      - name: empty-lines
      - name: error-return
      - name: get-return
      - name: identical-branches
      - name: if-return
      - name: import-shadowing
      - name: increment-decrement
      - name: indent-error-flow
      - name: modifies-parameter
      - name: modifies-value-receiver
      - name: range
      - name: receiver-naming
      - name: redefines-builtin-id
      - name: superfluous-else
      - name: time-equal
      - name: unconditional-recursion
      - name: unexported-return
      - name: unnecessary-stmt
      - name: use-any
      - name: useless-break
      - name: var-declaration
      - name: waitgroup-by-value
      - name: function-result-limit
        arguments:
          - 4
linters:
  disable-all: true
  enable:
    - exportloopref
    - funlen
    - noctx
    - gocritic
    - nestif
    - exhaustive
    - nilerr
    - revive
    - prealloc
    - staticcheck
    - errorlint
    - gomnd
    - misspell
    - errname
    - typecheck
    - rowserrcheck
    - thelper
    - tparallel
    - asciicheck
    - testpackage
    - nolintlint
    - gosec
    - gomodguard
    - unused
    - unconvert
    - paralleltest
    - gosimple
    - goprintffuncname
    - gocyclo
    - predeclared
    - gofumpt
    - cyclop
    - goconst
    - errcheck
    - forcetypeassert
    - sqlclosecheck
    - ineffassign
    - unparam
    - nakedret
    - promlinter
    - durationcheck
    - govet
    - dupl
    - tagliatelle
    - wrapcheck
    - wastedassign
    - lll
    - makezero
    - bodyclose
    - stylecheck
    - whitespace
    - loggercheck
    - maintidx
    - asasalint
    - bidichk
    - nosprintfhostport
    - usestdlibvars
    - musttag
    - reassign
    - testableexamples
    - nilnil
    - tenv
    - execinquery
    - gocheckcompilerdirectives
    - grouper
    - goimports
    - decorder
issues:
  max-same-issues: 50
  exclude-rules:
    - text: 'shadow: declaration of "(err|ctx)" shadows declaration at'
      linters:
        - govet
    - source: (noinspection|TODO)
      linters:
        - godot
    - source: //noinspection
      linters:
        - gocritic
    - linters:
        - staticcheck
      text: 'SA1019:'
      path: pkg/.*
    - linters:
        - lll
      source: '^//go:generate '
    - path: _test\.go
      linters:
        - bodyclose
        - dupl
        - funlen
        - goconst
        - gosec
        - noctx
        - wrapcheck
run:
  tests: false
  concurrency: 4
  timeout: 5m
  issues-exit-code: 2
  skip-files:
    - .*\.validate\.go$
    - .*\.pb\.go$
